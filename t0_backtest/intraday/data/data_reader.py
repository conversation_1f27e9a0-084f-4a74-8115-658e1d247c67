import pandas as pd
from data import data_path
from utils import mysql
import os
from utils import trading_calendar

def get_arron_signals_bt(date,sym):
    pt=data_path.file_aaron_signal_bt_daily_sym(date,sym)
    if not os.path.exists(pt):
        return None
    try:
        df= pd.read_csv(pt)
    except Exception as e:
        return None
    return df

def get_arron_signals_prod(date,sym):
    pt=data_path.file_aaron_signal_prd_daily_sym(date,sym)
    if not os.path.exists(pt):
        return None
    try:
        df= pd.read_csv(pt)
    except Exception as e:
        return None
    return df

def get_aaron_signals_raw_data_prod(date):
    pt=data_path.dir_aaron_raw_signal_prd_daily(date)
    if not os.path.exists(pt):
        return None
    df= pd.read_csv(pt)
    return df

def get_aaron_signals_raw_data_bt(date):
    pt=data_path.dir_aaron_raw_signal_bt_daily(date)
    if not os.path.exists(pt):
        return None
    df= pd.read_csv(pt)
    return df


def get_close_pxs_by_date_from_mysql(date):
    df=mysql.query(mysql.get_datayes_db_connection(),"select TICKER_SYMBOL,CLOSE_PRICE from datayes.mkt_equd_adj where TRADE_DATE='{}'".format(date))
    return {_['TICKER_SYMBOL']:_['CLOSE_PRICE'] for _ in  df.to_dict('records')}

def get_stk_price_from_file(date):
    df=pd.read_feather(data_path.file_stk_price_daily(date))
    return df

def get_close_pxs_by_date_from_file(date):
    df=get_stk_price_from_file(date)
    return {d['TICKER_SYMBOL']:d['CLOSE_PRICE'] for d in df.to_dict('records')}

def get_preclose_pxs_by_date_from_file(date):
    pdate=trading_calendar.get_prev_trade_date(date)
    df=get_stk_price_from_file(pdate)
    return {d['TICKER_SYMBOL']:d['CLOSE_PRICE'] for d in df.to_dict('records')}

def get_snapshot_feather_data_by_date(date):
    pt=data_path.file_processed_snapshot_daily(date)
    df=pd.read_feather(pt)
    return df

def get_orderbook_stats_data_by_date(date):
    pt=data_path.file_orderbook_stats_daily(date)
    df=pd.read_feather(pt)
    return df
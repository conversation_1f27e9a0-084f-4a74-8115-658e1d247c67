import sys
sys.path.append("/home/<USER>/py/stk_py")
import pandas as pd
from utils import mysql
import datetime
import numpy as np
# from data import data_reader

def open_position(slc):
    p={}
    p['side']=slc['side']
    p['id']=slc['id']
    p['pid']=slc['pid']
    p['price']=slc['filled_price']
    p['qty']=slc['filled_quantity']
    p['sym']=slc['symbol']
    p['time']=slc['create_time']
    p['account_id']=slc['account_id']
    return p

def close_position(p,slc):
    t={}
    t['open_side']=p['side']
    t['open_id']=p['id']
    t['pid']=p['pid']
    t['account_id']=p['account_id']
    t['open_price']=p['price']
    t['sym']=p['sym']
    t['open_time']=p['time']
    t['close_side']=slc['side']
    t['close_id']=slc['id']
    t['close_price']=slc['filled_price']
    t['close_time']=slc['create_time']
    if p['qty']==slc['filled_quantity']:
        t['qty']=p['qty']
        return t,None,None
    if p['qty']>slc['filled_quantity']:
        t['qty']=slc['filled_quantity']
        p['qty']=p['qty']-slc['filled_quantity']
        return t,p,None
    if p['qty']<slc['filled_quantity']:
        t['qty']=p['qty']
        slc['filled_quantity']=slc['filled_quantity']-p['qty']
        return t,None,slc

def get_trds(slcs):
    slcs=slcs[slcs['filled_quantity']>0]
    pos=[]
    trds=[]
    for slc in slcs.to_dict('records'):
        if len(pos)==0:
            pos.append(open_position(slc))
        elif pos[0]['side']==slc['side']:
            pos.append(open_position(slc))
        else:
            for i,p in enumerate(pos) :
                t,p,slc=close_position(p,slc)
                trds.append(t)
                if p is not None:
                    pos[i]=p
                    break
                else:
                    pos[i]=None
                if slc is not None:
                    pos[i]=None
                else:
                    break
            if slc is not None:
                pos.append(open_position(slc))
        pos=[_ for _ in pos if _ is not None]
    return pos,trds

def get_fee_with_position(pos,commission_dict):
    commission=commission_dict.get(pos['account_id'],3)/10000
    if pos['side']==1:
        return (pos['price']*pos['qty'])*commission+(pos['midpx']*pos['qty'])*(0.0005+commission)
    else:
        return (pos['midpx']*pos['qty'])*commission+(pos['price']*pos['qty'])*(0.0005+commission)
    
def get_fee_with_trd(trd,commission_dict):
    commission=commission_dict.get(trd['account_id'],3)/10000
    if trd['open_side']==1:
        return (trd['open_price']*trd['qty'])*commission + (trd['close_price']*trd['qty'])*(0.0005+commission)
    else:
        return (trd['open_price']*trd['qty'])*(0.0005+commission) + (trd['close_price']*trd['qty'])*commission

def get_commission_dict():
    # df2=mysql.query(mysql.get_kuanfu_db_connection(),"select * from intraday_trading_config")
    # return {d['account_id']:d['commission'] for d in df2.to_dict('records')}
    return {'kf1000zz1h_pa': 1.1,'kfhtzx2h_zx': 1.5,'kfjpzgzx_zj': 1.2,'kfzx2h_gf': 1.5,'kfzhzx_ms': 1.6,'kfzx7h_hj':1.2,'yhaxzx6h_yh':1.2,'test_account_w3':3,'test_account_w2':2,
            'test_account_w4':4}

def intraday_trading_stats(p,slcs):
    if len(slcs)==0:
        cancel_rate=0
        error_rate=0
    else:
        cancel_rate=len(slcs[slcs['status']=='CANCELED'])/len(slcs)
        error_rate=len(slcs[slcs['status']=='ERROR'])/len(slcs)
    holding_amt=(p['price']*p['quantity']).sum()
    trading_amt=(p['amt_b']+p['amt_s']+p['diff_amt'].abs()).sum()
    profit_without_fee=p['profit'].sum()
    profit_after_fee=p['profit'].sum()-p['fee'].sum()
    holding_ret=profit_after_fee/holding_amt
    trading_ret=profit_after_fee/trading_amt
    position=p['diff_amt'].abs().sum()
    turnover=((p['filledQuantity_b']+p['filledQuantity_s'])*p['price']).sum()/(p['price']*p['quantity']).sum()
    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position}
    return d

def make_empty_trds():
    return pd.DataFrame(columns=['open_side', 'open_id', 'pid', 'account_id', 'open_price', 'sym',
       'open_time', 'close_side', 'close_id', 'close_price', 'close_time',
       'qty', 'fee', 'profit','open_amt','close_amt'])
def make_empty_pos():
    return pd.DataFrame(columns=['side','id','pid','price','qty','sym','time','account_id','amt','profit','fee'])

def get_exposure(ords):
    exposure=[]
    buy_amt=0
    sell_amt=0
    for o in ords.to_dict('records'):
        if o['side']==1:
            buy_amt+=(o['filled_quantity']*o['filled_price'])
        else:
            sell_amt+=(o['filled_quantity']*o['filled_price'])
        exposure.append(buy_amt-sell_amt)
    max_exposure=np.max(np.abs(exposure))
    max_long_exposure=np.max(np.positive(exposure))
    return max_exposure,max_long_exposure



def stats_by_pord(pord,trds,pos,ords):
       trds=trds.copy()
       trds['open_amt']=trds['open_price']*trds['qty']
       trds['close_amt']=trds['close_price']*trds['qty']
       trds['profit_ac']=trds['profit']-trds['fee']
       pos['amt']=pos['price']*pos['qty']
       long_trds=trds[trds['open_side']>0]
       short_trds=trds[trds['open_side']<0]
       long_pos=pos[pos['side']>0]
       short_pos=pos[pos['side']<0]
       d={}
       d['id']=pord['id']
       d['round_num']=len(trds)
       d['win_round_num']=len(trds[trds['profit']>0])
       d['win_round_num_ac']=len(trds[(trds['profit_ac'])>0])
       d['profit_round_amt']=trds[trds['profit']>0]['profit'].sum()
       d['profit_round_amt_ac']=trds[(trds['profit_ac'])>0]['profit_ac'].sum()
       d['long_round_num']=len(long_trds)
       d['win_round_num_long']=len(long_trds[long_trds['profit']>0])
       d['win_round_num_ac_long']=len(long_trds[(long_trds['profit_ac'])>0])
       d['profit_round_amt_long']=long_trds[long_trds['profit']>0]['profit'].sum()
       d['profit_round_amt_ac_long']=long_trds[(long_trds['profit_ac'])>0]['profit_ac'].sum()
       d['short_round_num']=len(short_trds)
       d['win_round_num_short']=len(short_trds[short_trds['profit']>0])
       d['win_round_num_ac_short']=len(short_trds[(short_trds['profit_ac'])>0])
       d['profit_round_amt_short']=short_trds[short_trds['profit']>0]['profit'].sum()
       d['profit_round_amt_ac_short']=short_trds[(short_trds['profit_ac'])>0]['profit_ac'].sum()
       d['ord_num']=len(ords)
       d['cancel_ord_num']=len(ords[ords['status']==110])
       d['reject_ord_num']=len(ords[ords['status']==120])
       d['long_open_vol']=long_trds['qty'].sum()+long_pos['qty'].sum()
       d['long_open_amt']=long_trds['open_amt'].sum()+long_pos['amt'].sum()
       d['short_open_vol']=short_trds['qty'].sum()+short_pos['qty'].sum()
       d['short_open_amt']=short_trds['open_amt'].sum()+short_pos['amt'].sum()
       d['long_close_vol']=long_trds['qty'].sum()
       d['long_close_amt']=long_trds['close_amt'].sum()
       d['short_close_vol']=short_trds['qty'].sum()
       d['short_close_amt']=short_trds['close_amt'].sum()
       d['profit']=trds['profit'].sum()+pos['profit'].sum()
       d['fee']=trds['fee'].sum()+pos['fee'].sum()
       d['profit_ac']=d['profit']-d['fee']
       d['profit_long']=long_trds['profit'].sum()+long_pos['profit'].sum()
       d['fee_long']=long_trds['fee'].sum()+long_pos['fee'].sum()
       d['profit_long_ac']=d['profit_long']-d['fee_long']
       d['profit_short']=short_trds['profit'].sum()+short_pos['profit'].sum()
       d['fee_short']=short_trds['fee'].sum()+short_pos['fee'].sum()
       d['profit_short_ac']=d['profit_short']-d['fee_short']
       d['vol_open']=trds['qty'].sum()+pos['qty'].sum()
       d['amt_open']=trds['open_amt'].sum()+pos['amt'].sum()
       d['trd_amt']=(ords['filled_price']*ords['filled_quantity']).sum()
       d['position']=(pos['price']*pos['qty']).sum()
       d.update(pord)
       return d

def stats_by_pords(pords,slcs,pos,trds):
    slcs_group=slcs.groupby('pid')
    pos_group=pos.groupby('pid')
    trds_group=trds.groupby('pid')
    l=[]
    for pord in pords.to_dict('records'):
        slc=slcs_group.get_group(pord['id']) if pord['id'] in slcs_group.groups else None
        if slc is None:
            l.append(pord)
            continue
        trd=trds_group.get_group(pord['id']) if pord['id'] in trds_group.groups else make_empty_trds()
        position=pos_group.get_group(pord['id']) if pord['id'] in pos_group.groups else make_empty_pos()
        ret=stats_by_pord(pord,trd,position,slc)
        l.append(ret)
    return l


def stats(pords,slcs,pos,trds):
    if len(slcs)==0:
        cancel_rate=0
        error_rate=0
    else:
        cancel_rate=len(slcs[slcs['status']==110])/len(slcs[slcs['status']!=120])
        error_rate=len(slcs[slcs['status']==120])/len(slcs)
    holding_amt=(pords['price']*pords['quantity']).sum()
    trading_amt=slcs['amt'].sum()
    profit_without_fee=trds['profit'].sum()+0 if pos.empty else pos['profit'].sum()
    profit_after_fee=(trds['profit'].sum()-trds['fee'].sum())+(0 if pos.empty else pos['profit'].sum()- 0 if pos.empty else pos['fee'].sum())
    position=0 if pos.empty else (pos['price']*pos['qty']).sum()
    holding_ret=profit_after_fee/holding_amt
    trading_ret=profit_after_fee/trading_amt
    turnover=trading_amt/holding_amt
    win_ratio_before_fee=len(trds[(trds['profit'])>0])/len(trds)
    win_ratio_after_fee= len(trds[(trds['profit']-trds['fee'])>0])/len(trds)
    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position,'win_ratio_before_fee':win_ratio_before_fee,'win_ratio_after_fee':win_ratio_after_fee}
    return d

def pct_data_process(x):
    return round(x*100,1)

def display(l):
    df=pd.DataFrame(l)
    df['holding_ret']=df['holding_ret']*10000
    df['trading_ret']=df['trading_ret']*10000
    df['turnover']=df['turnover'].apply(pct_data_process)
    df['error_rate']=df['error_rate'].apply(pct_data_process)
    df['cancel_rate']=df['cancel_rate'].apply(pct_data_process)
    df['win_ratio_before_fee']=df['win_ratio_before_fee'].apply(pct_data_process)
    df['win_ratio_after_fee']=df['win_ratio_after_fee'].apply(pct_data_process)
    df = df.applymap(lambda x: f"{x:.2f}" if isinstance(x, float) else x)
    df=df.rename(columns={'holding_amt':'持仓金额(元)', 'trading_amt':'交易金额(元)','profit_without_fee':'费前总收益(元)','profit_after_fee':'费后总收益(元)','holding_ret':'底仓收益率(bps)','trading_ret':'开仓收益率(bps)','turnover':'换手%','cancel_rate':'撤单率%','error_rate':'废单率%','win_ratio_before_fee':'费前胜率%','win_ratio_after_fee':'费后胜率%','position':'未平仓位(元)','group_name':'分组'})
    return df

def run_by_account(pords,slcs,date):
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    slcs['side']=slcs['operation'].apply(lambda x:-1 if x==110 else 1)
    slcs['amt']=slcs['filled_price']*slcs['filled_quantity']
    pords['price']=pords['symbol'].apply(lambda x:pxs.get(x,0))
    slcs=slcs.sort_values('create_time')
    poslist=[]
    trdlist=[]
    for pid,g in slcs.groupby('pid'):
        pos,trds=get_trds(g)
        if len(pos)>0:
            poslist=poslist+pos
        if len(trds)>0:
            trdlist=trdlist+trds
    pos=pd.DataFrame(poslist)
    trds=pd.DataFrame(trdlist)
    if not pos.empty:
        pos['midpx']=pos['sym'].apply(lambda x:pxs.get(x,0))
        pos['profit']=(pos['price']-pos['midpx'])*pos['qty']*-pos['side']
        pos['fee']=pos.apply(lambda x:get_fee_with_position(x,{}),axis=1)
    trds['fee']=trds.apply(lambda x:get_fee_with_trd(x,{}),axis=1)
    trds['profit']=(trds['open_price']-trds['close_price'])*trds['qty']*-trds['open_side']
    ret=stats(pords,slcs,pos,trds)
    ret['date']=date
    return ret


def get_transaction_and_position(pords,slcs,pxs):
    commission_dict=get_commission_dict()
    slcs['side']=slcs['operation'].apply(lambda x:-1 if x==110 else 1)
    slcs['amt']=slcs['filled_price']*slcs['filled_quantity']
    pords['price']=pords['symbol'].apply(lambda x:pxs.get(x,0))
    slcs=slcs.sort_values('create_time')
    poslist=[]
    trdlist=[]
    for pid,g in slcs.groupby('pid'):
        pos,trds=get_trds(g)
        if len(pos)>0:
            poslist=poslist+pos
        if len(trds)>0:
            trdlist=trdlist+trds
    pos=pd.DataFrame(poslist)
    trds=pd.DataFrame(trdlist)
    if not pos.empty:
        pos['midpx']=pos['sym'].apply(lambda x:pxs.get(x,0))
        pos['profit']=(pos['price']-pos['midpx'])*pos['qty']*-pos['side']
        pos['fee']=pos.apply(lambda x:get_fee_with_position(x,commission_dict),axis=1)
    trds['fee']=trds.apply(lambda x:get_fee_with_trd(x,commission_dict),axis=1)
    trds['profit']=(trds['open_price']-trds['close_price'])*trds['qty']*-trds['open_side']
    if pos.empty:
        pos=make_empty_pos()
    return pos,trds

def stats_agg_by_sym(df):
    d={}
    d['hold_amt']=(df['price']*df['quantity']).mean()
    d['fee']=df['fee'].sum()
    d['trd_amt']=df['trd_amt'].sum()
    d['turnover']=d['trd_amt']/d['hold_amt']
    d['profit']=df['profit'].sum()
    d['profit_ac']=d['profit']-d['fee']
    d['ret2hold']=d['profit']/d['hold_amt']
    d['ret2hold_ac']=d['profit_ac']/d['hold_amt']
    d['round_num']=df['round_num'].sum()
    d['win_rate_ac']=df['win_round_num_ac'].sum()/d['round_num']
    d['win_loss_ratio_ac']=(df['profit_round_amt_ac'].sum()/df['win_round_num_ac'].sum())/abs(((df['profit_ac'].sum()-df['profit_round_amt_ac'].sum())/(d['round_num']-df['win_round_num_ac'].sum())))
    return d

def stats_agg_by_date(df):
    d={}
    d['hold_amt']=(df['price']*df['quantity']).sum()
    d['trd_amt']=df['trd_amt'].sum()
    d['fee']=df['fee'].sum()
    d['profit']=df['profit'].sum()
    d['profit_ac']=d['profit']-d['fee']
    d['position']=df['position'].sum()
    d['turnover']=d['trd_amt']/d['hold_amt']
    d['trd_rate']=len(df[df['trd_amt']>0])/len(df)
    d['cancel_rate']=df['cancel_ord_num'].sum()/(df['ord_num'].sum()-df['reject_ord_num'].sum())
    d['ret2trade']=d['profit']/d['trd_amt']
    d['ret2trade_ac']=d['profit_ac']/d['trd_amt']
    d['ret2hold']=d['profit']/d['hold_amt']
    d['ret2hold_ac']=d['profit_ac']/d['hold_amt']
    d['trd_amt_long']=(df['long_open_amt'].sum()+df['long_close_amt'].sum())
    d['profit_long']=df['profit_long'].sum()
    d['profit_long_ac']=(df['profit_long'].sum()-df['fee_long'].sum())
    d['ret2trade_long']=d['profit_long']/d['trd_amt_long']
    d['ret2hold_long']=d['profit_long']/d['hold_amt']
    d['ret2trade_long_ac']=d['profit_long_ac']/d['trd_amt_long']
    d['ret2hold_long_ac']=d['profit_long_ac']/d['hold_amt']
    d['trd_amt_short']=(df['short_open_amt'].sum()+df['short_close_amt'].sum())
    d['profit_short']=df['profit_short'].sum()
    d['profit_short_ac']=(df['profit_short'].sum()-df['fee_short'].sum())
    d['ret2trade_short']=d['profit_short']/d['trd_amt_short']
    d['ret2hold_short']=d['profit_short']/d['hold_amt']
    d['ret2trade_short_ac']=d['profit_short_ac']/d['trd_amt_short']
    d['ret2hold_short_ac']=d['profit_short_ac']/d['hold_amt']
    d['round_num']=df['round_num'].sum()
    d['order_num']=df['ord_num'].sum()
    d['long_round_num']=df['long_round_num'].sum()
    d['short_round_num']=df['short_round_num'].sum()
    d['win_rate']=df['win_round_num'].sum()/d['round_num']
    if len(df[df['trd_amt']>0])>0:
        d['win_rate_sym']=len(df[df['profit']>0])/len(df[df['trd_amt']>0])
        d['win_rate_sym_ac']=len(df[df['profit_ac']>0])/len(df[df['trd_amt']>0])
    else:
        d['win_rate_sym']=0
        d['win_rate_sym_ac']=0    
    d['win_rate_long']=df['win_round_num_long'].sum()/d['long_round_num']
    d['win_rate_short']=df['win_round_num_short'].sum()/d['short_round_num']
    d['win_rate_ac']=df['win_round_num_ac'].sum()/d['round_num']
    d['win_rate_long_ac']=df['win_round_num_ac_long'].sum()/d['long_round_num']
    d['win_rate_short_ac']=df['win_round_num_ac_short'].sum()/d['short_round_num']
    d['win_loss_ratio']=(df['profit_round_amt'].sum()/df['win_round_num'].sum())/abs(((df['profit'].sum()-df['profit_round_amt'].sum())/(d['round_num']-df['win_round_num'].sum())))
    d['win_loss_ratio_ac']=(df['profit_round_amt_ac'].sum()/df['win_round_num_ac'].sum())/abs(((df['profit_ac'].sum()-df['profit_round_amt_ac'].sum())/(d['round_num']-df['win_round_num_ac'].sum())))
    d['win_loss_ratio_long']=(df['profit_round_amt_long'].sum()/df['win_round_num_long'].sum())/abs(((df['profit_long'].sum()-df['profit_round_amt_long'].sum())/(d['long_round_num']-df['win_round_num_long'].sum())))
    d['win_loss_ratio_long_ac']=(df['profit_round_amt_ac_long'].sum()/df['win_round_num_ac_long'].sum())/abs(((df['profit_long_ac'].sum()-df['profit_round_amt_ac_long'].sum())/(d['long_round_num']-df['win_round_num_ac_long'].sum())))
    d['win_loss_ratio_short']=(df['profit_round_amt_short'].sum()/df['win_round_num_short'].sum())/abs(((df['profit_short'].sum()-df['profit_round_amt_short'].sum())/(d['short_round_num']-df['win_round_num_short'].sum())))
    d['win_loss_ratio_short_ac']=(df['profit_round_amt_ac_short'].sum()/df['win_round_num_ac_short'].sum())/abs(((df['profit_short_ac'].sum()-df['profit_round_amt_ac_short'].sum())/(d['short_round_num']-df['win_round_num_ac_short'].sum())))
    d['max_exposure']=df['max_exposure'].iloc[0]
    d['max_long_exposure']=df['max_long_exposure'].iloc[0]
    d['max_exposure_rate']=d['max_exposure']/d['hold_amt']
    d['max_long_exposure_rate']=d['max_long_exposure']/d['hold_amt']
    return d

def stats_agg_annually(df):
    d={}
    d['day_num']=len(df)
    d['avg_hold_amt']=df['hold_amt'].mean()
    d['avg_trd_amt']=df['trd_amt'].mean()
    d['fee']=df['fee'].sum()
    d['profit']=df['profit'].sum()
    d['profit_ac']=df['profit_ac'].sum()
    d['avg_turnover']=df['turnover'].mean()
    d['annual_turnover']=df['turnover'].mean() *250
    d['ret2hold']=df['ret2hold_ac'].sum()
    d['annual_ret2hold']=(df['ret2hold_ac'].mean())*250
    # d['sym_win_rate']=df['sym_win_rate_ac'].mean()
    d['day_win_rate']=len(df[df['profit_ac']>0])/d['day_num']
    d['max_exposure_rate']=df['max_exposure_rate'].max()
    d['max_long_exposure_rate']=df['max_long_exposure_rate'].max()
    df2=pd.DataFrame([d])
    df2['avg_hold_amt']=df2['avg_hold_amt'].apply(amt2w)
    df2['avg_trd_amt']=df2['avg_trd_amt'].apply(amt2w)
    df2['fee']=df2['fee'].round(1)
    df2['profit']=df2['profit'].round(1)
    df2['profit_ac']=df2['profit_ac'].round(1)
    df2['avg_turnover']=df2['avg_turnover'].apply(val2pct)
    df2['annual_turnover']=df2['annual_turnover'].round(0)
    df2['ret2hold']=df2['ret2hold'].apply(val2bps)
    df2['annual_ret2hold']=df2['annual_ret2hold'].apply(val2pct)
    df2['day_win_rate']=df2['day_win_rate'].apply(val2pct)
    df2['max_exposure_rate']=df2['max_exposure_rate'].apply(val2pct)
    df2['max_long_exposure_rate']=df2['max_long_exposure_rate'].apply(val2pct)
    df2=df2.rename(columns={'day_num':'交易天数', 'avg_hold_amt':'平均底仓金额（万元）', 'avg_trd_amt':'平均交易金额（万元）', 'fee':'费用', 'profit':'盈利', 'profit_ac':'费后盈利',
       'avg_turnover':'平均换手%', 'annual_turnover':'年化换手（倍）', 'ret2hold':'底仓收益(bps)', 'annual_ret2hold':'年化底仓收益%',
       'day_win_rate':'日胜率%', 'max_exposure_rate':'最大敞口%', 'max_long_exposure_rate':'最大资金占用%'})
    return df2

datedf_name_dict={'hold_amt':'底仓金额(万元)', 'trd_amt':'交易金额(万元)', 'fee':'费用(元)', 'profit':'盈利(元)', 'profit_ac':'费后盈利(元)', 'turnover':'换手%','trd_rate':"开仓率%",
       'ret2trade':'开仓收益率(bps)', 'ret2trade_ac':'费后开仓收益率(bps)', 'ret2hold':'底仓收益率(bps)', 'ret2hold_ac':'费后底仓收益率(bps)', 'trd_amt_long':'开多金额(万元)',
       'profit_long':'开多收益(元)', 'profit_long_ac':'费后开多收益(元)', 'ret2trade_long':'开多开仓收益率(bps)', 'ret2hold_long':'开多底仓收益率(bps)',
       'ret2trade_long_ac':'费后多仓开仓收益(元)', 'ret2hold_long_ac':'费后多仓底仓收益率(bps)', 'trd_amt_short':'开空金额(万元)',
       'profit_short':'开空收益(元)', 'profit_short_ac':'费后开空收益(元)', 'ret2trade_short':'开空开仓收益率(bps)', 'ret2hold_short':'费后开空底仓收益率(bps)',
       'ret2trade_short_ac':'费后开空开仓收益率(bps)', 'ret2hold_short_ac':'费后开空底仓收益率(bps)', 'round_num':'回合数', 'order_num':'委托数',
       'long_round_num':'开多数', 'short_round_num':'开空数', 'win_rate':'单笔胜率%', 'win_rate_long':'单笔开多胜率%',
       'win_rate_short':'单笔开空胜率%', 'win_rate_ac':'单笔费后胜率%','win_rate_sym':'单票胜率%','win_rate_sym_ac':'单票费后胜率%', 'win_rate_long_ac':'单笔费后开多胜率%',
       'win_rate_short_ac':'单笔费后开空胜率%', 'win_loss_ratio':'盈亏比', 'win_loss_ratio_ac':'费后盈亏比',
       'win_loss_ratio_long':'开多盈亏比', 'win_loss_ratio_long_ac':'开多费后盈亏比', 'win_loss_ratio_short':'开空盈亏比',
       'win_loss_ratio_short_ac':'费后开空盈亏比', 'date':'日期','max_exposure':'最大资金敞口','max_long_exposure':'最大资金占用','max_exposure_rate':'最大敞口率%','max_long_exposure_rate':'最大资金占用率%','cancel_rate':'撤单率%','position':'未平仓位(万元)'}

def amt2w(x):
    return round(x/10000,2)

def val2pct(x):
    return round(x*100,1)

def val2bps(x):
    return round(x*10000,1)

def fmt2display(df):
    df['hold_amt']=df['hold_amt'].apply(amt2w)
    df['trd_amt']=df['trd_amt'].apply(amt2w)
    df['fee']=df['fee'].round(2)
    df['profit']=df['profit'].round(2)
    df['profit_ac']=df['profit_ac'].round(2)
    df['turnover']=df['turnover'].apply(val2pct) 
    df['ret2trade_ac']=df['ret2trade_ac'].apply(val2bps)
    df['ret2hold_ac']=df['ret2hold_ac'].apply(val2bps)
    df['win_rate_ac']=df['win_rate_ac'].apply(val2pct)
    df['win_rate_sym_ac']=df['win_rate_sym_ac'].apply(val2pct)
    df['max_exposure_rate']=df['max_exposure_rate'].apply(val2pct)
    df['max_long_exposure_rate']=df['max_long_exposure_rate'].apply(val2pct)
    df['win_loss_ratio_ac']=df['win_loss_ratio_ac'].round(2)
    df=df[['hold_amt','trd_amt','profit','profit_ac','turnover','ret2trade_ac','ret2hold_ac','win_rate_ac','win_rate_sym_ac','win_loss_ratio_ac','max_exposure_rate','max_long_exposure_rate','date']]
    rename_dict = {key: datedf_name_dict[key] for key in df.columns if key in datedf_name_dict}
    df=df.rename(columns=rename_dict)
    return df

def run(pords,slcs,date):
    commission_dict={'kf1000zz1h_pa': 1.1,'kfhtzx2h_zx': 1.5,'kfjpzgzx_zj': 1.2,'kfzx2h_gf': 1.5,'kfzhzx_ms': 1.6}
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    slcs['side']=slcs['operation'].apply(lambda x:-1 if x==110 else 1)
    slcs['amt']=slcs['filled_price']*slcs['filled_quantity']
    pords['price']=pords['symbol'].apply(lambda x:pxs.get(x,0))
    slcs=slcs.sort_values('create_time')
    poslist=[]
    trdlist=[]
    for pid,g in slcs.groupby('pid'):
        pos,trds=get_trds(g)
        if len(pos)>0:
            poslist=poslist+pos
        if len(trds)>0:
            trdlist=trdlist+trds
    pos=pd.DataFrame(poslist)
    trds=pd.DataFrame(trdlist)
    if not pos.empty:
        pos['midpx']=pos['sym'].apply(lambda x:pxs.get(x,0))
        pos['profit']=(pos['price']-pos['midpx'])*pos['qty']*-pos['side']
        pos['fee']=pos.apply(lambda x:get_fee_with_position(x,commission_dict),axis=1)
    trds['fee']=trds.apply(lambda x:get_fee_with_trd(x,commission_dict),axis=1)
    trds['profit']=(trds['open_price']-trds['close_price'])*trds['qty']*-trds['open_side']
    l=[]
    ret=stats(pords,slcs,pos,trds)
    ret['group_name']='total'
    l.append(ret)
    for name,g in pords.groupby('account_id'):
        ret=stats(g,slcs[slcs['account_id']==name],pos if pos.empty else pos[pos['account_id']==name],trds[trds['account_id']==name])
        ret['group_name']=name
        l.append(ret)
    for name,g in trds.groupby('open_side'):
        ret=stats(pords[pords['id'].isin(g['pid'])],slcs[slcs['pid'].isin(g['pid'])],pos if pos.empty else pos[pos['pid'].isin(g['pid'])],g)
        ret['group_name']='long' if name==1 else 'short'
        l.append(ret)
    return l,pos,trds

def get_parentorders(date):
        sql="select * from intraday_parentorders where date='{}'".format(date)
        df=mysql.query(mysql.get_trading_data_db_connection_new(),sql)
        df=df.fillna(0)
        return df
    
def get_slices(pids):
    ords=mysql.query(mysql.get_trading_data_db_connection_new(),"select * from orders where pid in {}".format(tuple(pids)))
    ords['create_time']=pd.to_datetime(ords['create_time'],unit='ms')+datetime.timedelta(hours=8)
    ords['last_upd_time']=pd.to_datetime(ords['last_upd_time'],unit='ms')+datetime.timedelta(hours=8)
    return ords

def run_by_date(date):
    pords=get_parentorders(date)
    slcs=get_slices(pords['id'].unique())
    l,pos,trds=run(pords,slcs,date)
    # if not pos.empty:
    #     pos.to_parquet("/home/<USER>/py/stock-py/intraday/tca/data/intraday/pos_{}.par".format(date),index=False)
    # trds.to_parquet("/home/<USER>/py/stock-py/intraday/tca/data/intraday/trds_{}.par".format(date),index=False)
    display(l)

if __name__=="__main__":
    # for date in calendar_data.get_trading_dates("20241210","20241210"):
    for date in ["20250117","20250120","20250121","20250122"]:
        print(date)
        run_by_date(date)

from ast import arg
from joblib import Parallel, delayed
from pqdm.processes import pqdm
import os, sys
import pandas as pd
import datetime
import pickle
from pathlib import Path


# notebook_dir = Path.cwd() 
# print(str(notebook_dir.parent))
# # sys.path.insert(0, str(notebook_dir.parent))

# print(sys.path)
from backtest import backtest as bt
from tca import tca
from tca import stk_tools


def pickle_dump(data,pt):
    with open(pt, 'wb') as file:
        pickle.dump(data, file)

def pickle_load(pt):
    with open(pt, 'rb') as file:
        return pickle.load(file)
    
    
def run_rongshuhai_inventory(date,pords):
    try:
        open_th=0.004
        ret=bt.run_by_date(date,open_th,pords,isbt=True,rt_raw_tca=True,filt_sig_by_time=True)
        # print(f'run {date} ret {ret}')
        return ret
    except Exception as e:
        print("run {} failed {}".format(date,e))
        return None
    
    
    
"""
## 回测数据格式
-  dict= {'********':[{'id': 'ccf48cd5-83a9-41cf-b0d6-dea305d42635',  'symbol': '000002',  'quantity': 36400,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)}, {'id': 'fe52a5b4-c0d8-4097-b88e-1fc2fdd045e0',  'symbol': '000063',  'quantity': 33000,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)},]}

"""


# date = '********'
cur_dir = os.path.dirname(os.path.abspath(__file__))
# file_1 = os.path.join(cur_dir, './', "inventory/A500_rmtop20%_2024.csv")
# file_2 = os.path.join(cur_dir, './', "inventory/daiwenzheng_inventory_processed.csv")

# rsh_name = 'hs300_0930_5m'
# rsh_name = 'hs300_0930_10m'
# rsh_name = 'zz1000_0930_5m'
# rsh_name = 'zz1000_0930_10m'
# rsh_name = 'zz2000_0930_5m'
# rsh_name = 'zz2000_0930_5m_集中'
# rsh_name = 'zz2000_0930_10m'
rsh_name = 'zz2000_0930_10m_集中'


file_3 = os.path.join(cur_dir, './', f"inventory/{rsh_name}.csv")


# demo1
# pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/A500_rmtop20%_2024.csv",date,"sym","qty",account='test_account_w3',filt_type='csv')
# pords=bt.read_positions(file_1,date,"sym","qty",account='test_account_w3',filt_type='csv')

# demo2
# pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/daiwenzheng_inventory_processed.csv",date,"symbol","qty",account='test_account_w3',filt_type='csv')
# pords=bt.read_positions(file_2,date,"symbol","qty",account='test_account_w3',filt_type='csv')



# demo3
# df=pd.read_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.csv")
def dump_rongshuhai_inventory_run_test():
    df=pd.read_csv(file_3)
    df.rename(columns={"Unnamed: 0": "date"}, inplace=True)
    d={}
    for data in df.to_dict('records'):
        date=data['date'][:10].replace('-', '')
        # if date<"********" or date>"********":
        if date<"********" :
            continue
        st=datetime.datetime.strptime(data['date'], '%Y-%m-%d %H:%M:%S')
        
        print(date)
        l=[]
        for k,v in data.items():
            if k=='date':
                continue
            if v==0:
                continue
            po=bt.make_parentorder(k.split(".")[0],int(v),"test_account_w3",date,start_time=datetime.datetime.strptime(date+"094200","%Y%m%d%H%M%S")) 
            l.append(po)
        d[date]=l

    stk_tools.pickle_dump(d, os.path.join(cur_dir, './', f"inventory/{rsh_name}.pkl"))


    # datas=stk_tools.pickle_load(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/hs300_pos_0935.pkl")
    # datas=stk_tools.pickle_load(os.path.join(cur_dir, './', f"inventory/{rsh_name}.pkl"))
    datas = d
    args=[]
    for k,v in datas.items():
        args.append([k,pd.DataFrame(v)])
        
    print(args[-1])
    # args[0][1].to_csv(os.path.join(cur_dir, './', "inventory/sample_zz2000_pos_0940.csv"))
        
        
    results=pqdm(args,run_rongshuhai_inventory,50,argument_type='args')

    l=[_ for _ in results if _ is not None]
    print(len(l))
    stk_tools.pickle_dump(l, os.path.join(cur_dir, './', f"results/2025/{rsh_name}.pkl"))


# stats
def stats_rongshuhai_inventory_run_test():
    # pt=r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl"
    pt = os.path.join(cur_dir, './', f"results/2025/{rsh_name}.pkl")
    perf_daily,perf_annually=bt.get_perf(pt)

    perf_daily.to_csv( os.path.join(cur_dir, './', f"results/2025/perf_daily_{rsh_name}.csv"),index=False)
    perf_annually.to_csv( os.path.join(cur_dir, './', f"results/2025/perf_annually_{rsh_name}.csv"),index=False)

    perf_symbol = bt.get_perf_by_sym(pt)
    perf_symbol.to_csv( os.path.join(cur_dir, './', f"results/2025/perf_symbol_{rsh_name}.csv"),index=False)



    # perf_daily.to_csv( r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_daily_hs300_pos_0935.csv",index=False)
    # perf_annually.to_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_annually_hs300_pos_0935.csv",index=False)


if __name__=="__main__":
    dump_rongshuhai_inventory_run_test()
    stats_rongshuhai_inventory_run_test()
    
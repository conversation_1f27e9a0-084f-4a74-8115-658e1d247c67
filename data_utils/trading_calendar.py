#%%
import datetime
import pandas as pd
import os

class TradingCalendar:
    def __init__(self, holiday_dates:pd.Series):
        """
        create a trading calendar object

        Args:
            dates (pd.Series): holidays dates, list or pd.Series type
        """
        # Convert the dates to pandas.Timestamp, then to datetime.date list
        # ensure dates only contain date information
        holiday_dates = pd.to_datetime(holiday_dates)
        self.holidays = [d.date() for d in holiday_dates]
        self.holidays.sort()  # Sort the holidays list
    
    def is_trading_day(self, date):
        date = self._convert_date(date)
        if date.weekday() >= 5 or self._binary_search(date, self.holidays):
            return False
        else:
            return True
    
    def next_trading_day(self, date):
        date = self._convert_date(date)
        next_date = date + datetime.timedelta(days=1)
        while not self.is_trading_day(next_date):
            next_date += datetime.timedelta(days=1)
        return next_date
    
    def last_trading_day(self, date):
        date = self._convert_date(date)
        last_date = date - datetime.timedelta(days=1)
        while not self.is_trading_day(last_date):
            last_date -= datetime.timedelta(days=1)
        return last_date
    
    def next_n_trading_days(self, date, n):
        trading_days = []
        current_date = self._convert_date(date)
        while len(trading_days) < n:
            current_date = self.next_trading_day(current_date)
            trading_days.append(current_date)
        return trading_days
    
    def last_n_trading_days(self, date, n):
        trading_days = []
        current_date = self._convert_date(date)
        while len(trading_days) < n:
            current_date = self.last_trading_day(current_date)
            trading_days.insert(0, current_date)
        return trading_days
    
    def trading_dates(self, start_date, end_date):
        trading_days = []
        current_date = self._convert_date(start_date)
        end_date = self._convert_date(end_date)
        while current_date <= end_date:
            if self.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date += datetime.timedelta(days=1)
        return trading_days
    
    def _binary_search(self, date, sorted_list:list):
        low = 0
        high = len(sorted_list) - 1
        while low <= high:
            mid = (low + high) // 2
            if sorted_list[mid] < date:
                low = mid + 1
            elif sorted_list[mid] > date:
                high = mid - 1
            else:
                return True
        return False
    
    # convert date input to datetime.date
    def _convert_date(self, date):
        if isinstance(date, int):
            date = str(date)
        elif isinstance(date, datetime.datetime):
            return date.date()
        return pd.to_datetime(date).date()
    

# %%
Calendar = TradingCalendar(holiday_dates=pd.read_csv(os.path.join(os.path.dirname(__file__),'holiday_non_weekend_days.csv'))['date'])

from anyio import wrap_file
from narwhals import Date
from misc.tools import find_latest_remote_file_on_date, get_stock_adj_close
from misc.ssh_conn import sftp_clent_zsdav, sftp_clent_wintrader
from misc.Readstockfile import update_remote_xlsx_getdf, update_remote_xlsx_putdf, read_remote_file, write_file

import os, sys

from misc.tools import get_index_constitutions_last_month
# name = find_latest_remote_file_on_date(
#     sftp_method=sftp_clent_zsdav,
#     dir= '超量子中泰/hold',
#     file_prefix='hold_',
#     date='20250610'
# )
# print(name)


# path = 'clz_zz1000账户超额带公式.xlsx'

# df = update_remote_xlsx_getdf(path, src_type='zsdav', sheet_name='clz_zz1000')
# df['test'] = 'test'
# print(df)
# update_remote_xlsx_putdf(df, file_type='xlsx', dest_type='zsdav', dest_path=path, sheet_name='clz_zz1000', index=False)



# get current working dir
# cur_dir = os.path.dirname(os.path.abspath(__file__))
# print(cur_dir)
# # get python path
# python_path = sys.path[0]
# print(python_path)


import pandas as pd
# file = f'指数成分.xlsx'

# for date in [
#     '20240101',
#     '20240201',    
#     '20240301',    
#     '20240401',    
#     '20240501',    
#     '20240601',    
#     '20240701',    
#     '20240801',    
#     '20240901',    
#     '20241001',    
#     '20241101',    
#     '20241201',    
#     '20250101',    
# ]:
# # date = '20240201'
#     df = get_index_constitutions_last_month('932000', date)

#     with pd.ExcelWriter(file, engine='openpyxl', mode='a') as writer:
#         df.to_excel(writer, sheet_name=date, index=False)


#     print(df)


# from misc.tools import get_stock_adj_close

# df = get_stock_adj_close('20250612')

# print(df.sort_values('ticker', ascending=False).head(10))

import pickle
def pickle_dump(data,pt):
    with open(pt, 'wb') as file:
        pickle.dump(data, file)

def pickle_load(pt):
    with open(pt, 'rb') as file:
        return pickle.load(file)
    
# file = '/home/<USER>/trade/t0_backtest/intraday/inventory/zz2000_pos_0940.pkl'

# df = pickle_load(file)

# print(type(df))
# # print(df.head(10))

# # df.head(10).to_csv('zz2000_pos_sample.csv', index=False)
# print(list(df.keys())[0])
# # print(df.keys())


# df = pd.DataFrame.from_dict(df, 'index')
# df = df.head(10)
# df.to_csv('zz2000_pos_sample.csv', index=False)


# file = '数据导出/中泰SmartX/自动导出/20250617/xtp_109156033251_Position.csv'


# df = read_remote_file(file, src_type='wintrader')
# print(df.head(10))

# write_file(df, file_type='csv', dest_type='dav', dest_path='xtp_109156033251_Position.csv', index=False)

# df = df.rename(columns={'总持仓: ''})
df = get_stock_adj_close('20250617')
print(df.head(3))
import traceback
import os, sys
import re
import datetime
import time
import argparse
from loguru import logger
import pandas as pd
import numpy as np
import warnings
# import sshtunnel
import pymysql
from tabulate import tabulate
import json
from urllib.parse import quote_plus as urlquote
from sqlalchemy import create_engine
import tempfile

from data_utils.trading_calendar import Calendar
from misc.ssh_conn import sftp_clent_zsdav
# sftp_clent_inner, sftp_clent_gtapi, sftp_clent_outer, sftp_clent_work, sftp_clent_trade, sftp_clent_trade2, sftp_clent_ninner
from misc.Readstockfile import read_file
from misc.Readstockfile import read_remote_file, write_file, append_dbf, append_csv
# from misc.standard_table import standardize_hold
from misc.hold_position_to_orders import adjust_changes
from misc.hold_position_to_orders import generate_order_files_list
from misc.utils import symbol_to_exchange_cn
from accounts_config.kf_accounts_basic import zs_accounts_dict, product_account_dict, test_accounts_dict
from accounts_config.accounts_fj import accounts_fj
# from misc.redis_func import df_read_redis, conn_redis
# from run_sub_process import run_sub_process
from misc.tools import find_latest_remote_file_on_date, get_product_short_value_from_pnl_fj, get_available_fund_from_pnl_file_fj
from misc.tools import get_latest_hold, get_target_by_date, get_st_stocks, get_stock_morning_hold
from misc.tools import get_product_future_hold_to_info, get_product_target_futures_unit
from misc.tools import get_datayes_dbconn
from misc.tools import get_cons_tag_series, calc_cons_ratio
from misc import VARS

# from test4 import trade_config, update_sub_config_in_main_config
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore') 


def get_trade_config(trade_config_file):
    trade_config = read_remote_file(trade_config_file, src_type='zsdav',
                                    dtype={'start_time':str, 'end_time':str, 'order_file_suffix':str}
                                    )
    if 'No.' in trade_config.columns:
        trade_config.drop(columns=['No.'], inplace=True)
    if 'flag' in trade_config.columns:
        trade_config['flag'] = trade_config['flag'].astype(str)
    trade_config.set_index('account_name', inplace=True)
    trade_config['order_file_suffix'] = trade_config['order_file_suffix'].replace(np.nan, '')
    trade_config = trade_config.replace(np.nan, None)
    trade_config = trade_config[trade_config['active'] == True]
    
    # print(trade_config)
    trade_config = trade_config.to_dict(orient='index') 
    # return dict
    return trade_config


def update_sub_config_in_main_config(main_config, sub_config_key, sub_config):
    # update trade_config into main config set kf_accounts_dict
    """
    main_config = {
        account_name : {
            ...
        }
    }
    sub_config = {
        account_name : {
            ...
        }
    }
    after merge:
    main_config = {
        account_name : {
            ...
            sub_config_key : sub_config
        }
    }
    """
    for account_name in sub_config.keys():
        if account_name not in main_config.keys():
            continue
        # kf_accounts_dict[account_name].update({'trade_config' : trade_config[account_name]})
        # update trade_config into main config set kf_accounts_dict
        if sub_config_key not in main_config[account_name].keys():
            main_config[account_name][sub_config_key] = {}
        for key in sub_config[account_name].keys():
            main_config[account_name][sub_config_key][key] = sub_config[account_name][key]
    return main_config


# def get_close(date):
#     # conn = pymysql.connect(host="**************", database="datayes", user="datayesRO",
#     #                     password="datayesRO", port=3306)
#     engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
#     # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     query_close="""
#     SELECT *
#     FROM mkt_equd

#     WHERE TRADE_DATE="{}"
#     """.format(date)
#     with engine.connect() as conn:
#         pre_close=pd.read_sql(query_close,conn)
#     pre_close = pre_close[['TICKER_SYMBOL', 'TRADE_DATE', 'CLOSE_PRICE']]
#     pre_close.columns = ['symbol', 'close_date', 'close']
#     pre_close['symbol'] = pre_close['symbol'].astype(int)
#     return pre_close

def get_close_adj(date):
    # conn = pymysql.connect(host="**************", database="datayes", user="datayesRO",
    #                     password="datayesRO", port=3306)
    engine = get_datayes_dbconn()
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    query_close="""
    SELECT 
        CONVERT( TICKER_SYMBOL, UNSIGNED INTEGER) as symbol,
        TRADE_DATE as close_date,
        CLOSE_PRICE_1 as close
    FROM mkt_equd_adj

    WHERE TRADE_DATE="{}"
    """.format(date)
    with engine.connect() as conn:
        pre_close=pd.read_sql(query_close,conn)
    # pre_close = pre_close[['TICKER_SYMBOL', 'TRADE_DATE', 'CLOSE_PRICE']]
    # pre_close.columns = ['symbol', 'close_date', 'close']
    # pre_close['symbol'] = pre_close['symbol'].astype(int)
    return pre_close    


def get_mkt_limit(date):
    # conn = pymysql.connect(host="**************", database="datayes", user="datayesRO",
    #                     password="datayesRO", port=3306)
    engine = get_datayes_dbconn()
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    query_mkt_limit="""
    SELECT * 
    FROM mkt_limit
    WHERE TRADE_DATE="{}"
    """.format(date)

    with engine.connect() as conn:
        mkt_limit=pd.read_sql(query_mkt_limit,conn)
    mkt_limit = mkt_limit[['TICKER_SYMBOL', 'LIMIT_UP_PRICE', 'LIMIT_DOWN_PRICE']]
    mkt_limit.columns = ['symbol', 'uplimit', 'downlimit']
    mkt_limit['symbol'] = mkt_limit['symbol'].astype(int)
    return mkt_limit

def get_index_close(index, date) -> float:
    engine = get_datayes_dbconn()
    query_close = """
    SELECT 
        TICKER_SYMBOL as ticker,
        CLOSE_INDEX as close
    FROM mkt_idxd
    WHERE 
    TICKER_SYMBOL IN ('{}')
    AND
    TRADE_DATE="{}"
    """.format(index, date)
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    with engine.connect() as conn:
        close = pd.read_sql(query_close, conn).set_index('ticker').loc[index, 'close']
    return close

def delta_endtime(delta_min, start='now'):
    """
    start : 'now' or 'HHMM'
    
    """
    if start == 'now':
        return (datetime.datetime.now()+datetime.timedelta(minutes=delta_min)).strftime('%H%M00')
    else:
        return (datetime.datetime.strptime(start, '%H%M')+datetime.timedelta(minutes=delta_min)).strftime('%H%M00')


def get_account_config(account_name, accounts_config, key):
    return accounts_config[account_name].get(key, {})

def get_account_exclude_flag(account_config):
    if ALL_ACCOUNTS == True:
        flag = True
    else:
        flag = account_config.get('exclude', False) ^ TOGGLE_EXCLUDE
    return flag

def filter_account_flag(account_config):
    # print(account_config.get('flag'))
    if FILTER_FLAG=='all' or account_config.get('flag') == FILTER_FLAG:
        flag = True
    else:
        flag = False
    return flag


# def get_target_position(account_name, date):
#     # fb_position in config
#     try:
#         # if config.get('new_system') == True:
#         position_file = find_latest_remote_file_on_date(sftp_clent_ninner, os.path.join(account_name, 'position'), file_prefix='position.', date=date)
#         logger.info(f'\n{account_name} ninner position: {position_file}')
#         df = read_remote_file(os.path.join(account_name, 'position', position_file), src_type='ninner',
#                             header=None, names=['symbol', 'target_shares']
#                             )
#         # else:
#         #     df = read_remote_file(os.path.join(account_name, 'position', f'position.{date}091000.csv'), src_type='inner',
#         #                         header=None, names=['symbol', 'target_shares']
#         #                         )
#     except FileNotFoundError:
#         df = pd.DataFrame({'symbol': [1], 'target_shares': [0]})
#         logger.warning(f'{account_name} position.{date}091000 not found, 用 0 替代')
#     return df

def custom_get_target_by_date(account_name, date, config=None):
    """
    cancelled....
        config: {
                    account_name: 
                        {
                            'new_system': True/False,
                        }
                }
    """
    # sftp_ftp = get_account_sftp(account_name)['sftp']
    # ftp_type = get_account_sftp(account_name)['ftp_type']
    sftp_ftp = sftp_clent_zsdav
    ftp_type = 'zsdav'
    try:
        # if isinstance(config, dict) and config.get(account_name).get('new_system'):
        
        if config.get('position_file') is not None:
            position_file = config['position_file']
        else:
            position_file = find_latest_remote_file_on_date(sftp_ftp, os.path.join(account_name, 'position'), file_prefix='position.', date=date)
        
        target_pos = read_remote_file(path=os.path.join(account_name, 'position', position_file),
                                src_type=ftp_type, header=None, names=['ticker', 'volume'])
        target_pos['ticker'] = target_pos['ticker'].astype('int')
            # print(account_name, 'new_account')
        # else: 
        #     target_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                             src_type='inner', header=None, names=['ticker', 'volume'])
    except FileNotFoundError:
        target_pos = pd.DataFrame([[1,0]], columns=['ticker', 'volume'])
        logger.warning('账户- {} 的目标持仓position文件不存在, 用 0 替代'.format(account_name))
    return target_pos


# def get_latest_hold(account_name, date, config):
#     # 如果指定了hold_file, 用指定设置, 先dav, 再inner
#     # 如果redis 没有, 先去dav上找date 这天的
#     # 如果dav 没有, 去inner ftp找 date这天的
#     # 如果inner ftp 没有date 的, 再找前一天 pre_date 的
#     # 如果都没有, 用 0 代替

#     # 多次尝试功能
#     def try_get_hold_methods(account_name, date):
#         methods = [get_hold_dav, get_hold_ninner, make_zero_hold]  # 将方法作为函数对象列表
#         for method in methods:
#             try:
#                 result = method(account_name=account_name, date=date)
#                 # print(f"{method.__name__} succeeded")
#                 return result
#             except Exception as e:
#                 print(f"{method.__name__} failed:", e)
#         raise Exception("All methods failed")

#     # dav 上当前date 的最新文件
#     def get_hold_dav(account_name, date):
#         file_name = find_latest_remote_file_on_date(
#             sftp_clent_dav,
#             os.path.join(account_name, "hold"),
#             file_prefix="hold_",
#             date=date,
#         )
#         logger.info(f'\n{account_name} dav hold: {file_name}')
#         df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type="dav")
#         return df

#     # ninner上当前 date 的最新文件
#     def get_hold_ninner(account_name, date):
#         file_name = find_latest_remote_file_on_date(
#             sftp_clent_ninner,
#             os.path.join(account_name, "hold"),
#             file_prefix="hold_",
#             date=date,
#         )
#         logger.info(f'\n{account_name} ninner hold: {file_name}')
#         df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type="ninner")
#         return df

#     def make_zero_hold(account_name, date):
#         df = pd.DataFrame({'ticker': [1], 'volume': [0], 'available_volume': [0]})
#         logger.warning(f'\n{account_name} hold on {date} not found anywhere, 用 0 替代')
#         return df
#     ############################################################
#     if config.get('hold_file') is not None:
#         if config['hold_file'] in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
#             df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type='dav')
#             logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in dav')
#         else:
#             df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type='ninner')
#             logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in ninner')
#     else:
#         # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#         df = df_read_redis(rs, key=f'{date}.stock.{account_name}.hold')
#         if df is None or df.empty:
#             df = try_get_hold_methods(account_name=account_name, date=date)

#             # if account_name in sftp_clent_dav.listdir('') and \
#             #     f'hold_{date}.xls' in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
#             #     df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='dav')
#             #     logger.info(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in dav')
#             # elif f'hold_{date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#             #     df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='ninner')
#             #     logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in ninner')
#             # # elif f'hold_{pre_date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#             # #     df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{pre_date}.xls'), src_type='inner')
#             # #     logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{pre_date}.xls in ninner')
#             # else:
#             #     df = pd.DataFrame({'ticker': [1], 'volume': [0]})
#             #     logger.warning(f'{account_name} hold_{date}.xls not found anywhere, 用 0 替代')

#     df = df.rename(columns={'ticker': 'symbol', 'volume': 'hold_shares',
#                             '代码' : 'symbol', '持仓量' : 'hold_shares',
#                             '证券代码' : 'symbol', '持有数量' : 'hold_shares', '当前数量' : 'hold_shares'})
#     return df


def check_clean_hold(account_name, hold, product_name):
    # ******** modify
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x>0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    hold['available_shares'] = hold['available_shares'].abs()
    
    # ==========================================================================
    # 过滤非股票
    is_stock_mask = hold['symbol'].map(lambda x: str(x).zfill(6)).map(lambda x : x[0] == '6' or x[0] == '3' or x[0:2] == '00')
    
    # is_stock_mask = hold['symbol'].isin(pre_close['symbol'])
    # warning symbols filtered
    if len(hold['symbol'][~is_stock_mask]) >0:
        logger.warning(f"\n过滤非股票代码: \n{account_name}\n     {list(hold['symbol'][~is_stock_mask])}\n\n\n")
        hold = hold[is_stock_mask]
    # print(hold.head(2))

    # 记录持仓市值
    hold = hold.merge(pre_close[['symbol','close']], on='symbol', how='left')
    hold['hold_value'] = hold['hold_shares'].mul(hold['close']).abs()
    long = hold[hold['direction'] == 1]
    short = hold[hold['direction'] == -1]
    long_hold_value = long['hold_value'].sum()
    short_hold_value = short['hold_value'].sum()
    
    # hold_value = hold.set_index('symbol')['hold_shares'].mul(pre_close.set_index('symbol')['close'], axis=0).abs().sum()
    update_records_info(account_records, account_name, 'long_hold_value', long_hold_value)
    update_records_info(account_records, account_name, 'short_hold_value', short_hold_value)
    
    update_records_info(product_records, product_name, 'product_long_hold_value',
        (product_records.get(product_name, {}).get('product_long_hold_value', 0) + long_hold_value))

    hold = hold[['symbol', 'hold_shares', 'direction', 'available_shares']]
    return hold

def check_clean_position(account_name, target, product_name):
    target['target_shares'] = target['target_shares'].astype(int)

    # ******** modify 
    # ==========================================================================
    target['direction'] = target['target_shares'].apply(lambda x: 1 if x>0 else -1)
    target['target_shares'] = target['target_shares'].abs()
    # ==========================================================================


    # 总的, 过滤 风险股 st股, 退市股
    ex_symbol = st_stocks['ticker'].tolist()
    st_mask = target['symbol'].isin(ex_symbol)
    if st_mask.sum() > 0:
        logger.warning(f'\n过滤st股, 退市股: \n{account_name}\n     {target[st_mask]}\n\n')
        target.loc[st_mask, 'target_shares'] = 0

    # 过滤不能交易的股票
    exclude_symbols = position_zero_by_account.get(account_name, []).copy()
    exclude_symbols = exclude_symbols + position_zero_lst_all_accounts.copy()
    if len(exclude_symbols) > 0 and target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
        logger.warning(f'\n过滤不能持有的股票: \n{account_name}\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
        target.loc[target['symbol'].isin(exclude_symbols), 'target_shares'] = 0
    
    
    # 黑名单, blacklist
    # 过滤不能交易的股票
    # if account_name in ['远航安心中性6号_国联', '宽辅联丰专享_国联']:
    #     # exclude symbols
    #     exclude_symbols = [601456, 2692, 600475, 600908]
    #     if target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
    #         logger.warning(f'过滤不能交易的股票: \n\n\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
    #     target = target[~target['symbol'].isin(exclude_symbols)]
    
    
    # if '远航安心中性3号' in account_name:
    #     exclude_symbols = [2736, 728, 600266, 689009]
    #     if target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
    #         logger.warning(f'过滤不能买入的股票: \n\n\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
    #     target.loc[target['symbol'].isin(exclude_symbols), 'target_shares'] = 0
    
    # if '宽辅泓涛专享1号_东方' in account_name:
    #     exclude_symbols = [601113]
    #     if target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
    #         logger.warning(f'过滤不能买入的股票: \n\n\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
    #     target.loc[target['symbol'].isin(exclude_symbols), 'target_shares'] = 0

    # if '远航安心中性6号_国君' in account_name:
    #     exclude_symbols = [601211, 688479, 2437, 600837]
    #     if target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
    #         logger.warning(f'过滤不能买入的股票: \n\n\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
    #     target.loc[target['symbol'].isin(exclude_symbols), 'target_shares'] = 0

    # if '宽辅金品中国专享' in account_name:
    #     exclude_symbols = [689009]
    #     if target[target["symbol"].isin(exclude_symbols)].shape[0] > 0:
    #         logger.warning(f'过滤不能买入的股票: \n\n\n     {target[target["symbol"].isin(exclude_symbols)]}\n\n')
    #     target.loc[target['symbol'].isin(exclude_symbols), 'target_shares'] = 0

    # 检查成分比例
    # if account_name in ['宽辅泓涛专享1号_东方', '宽辅泓涛专享1号']:
    #     cons_ratio = calc_cons_ratio(stocks=target, cons=constitutions, price=pre_close)
        # logger.warning(f'成分比例: \n{cons_ratio}')
        
        
    
    # 记录目标市值
    
    target = target.merge(pre_close[['symbol','close']], on='symbol', how='left')

    long = target[target['direction'] == 1]   # 多头
    short = target[target['direction'] == -1]  # 空头

    long_target_value = long['target_shares'].mul(long['close'], axis=0).abs().sum()
    short_target_value = short['target_shares'].mul(short['close'], axis=0).abs().sum()
    
    
    # long_target_value = target.set_index('symbol')['target_shares'].mul(pre_close.set_index('symbol')['close'], axis=0).abs().sum()
    update_records_info(account_records, account_name, 'target_value', long_target_value)
    update_records_info(account_records, account_name, 'short_target_value', short_target_value)
    
    update_records_info(product_records, product_name, 'product_target_value',
        (product_records.get(product_name, {}).get('product_target_value', 0) + long_target_value))

    target_future_num = long_target_value / zz500_preclose / zz500_multiplier
    update_records_info(product_records, product_name, 'product_target_future_count',
        (product_records.get(product_name, {}).get('product_target_future_count', 0) + target_future_num))

    # ------
    # try:
    #     target_future_info = product_future_target_unit.loc[product_name] * long_target_value
    # except KeyError:
    #     target_future_info = pd.Series({'HS300': 0, 'ZZ500': 0, 'ZZ1000': 0, 'ZZ2000': 0})
    # # 用1000 代替 2000
    # if 'ZZ2000' in target_future_info.index:
    #     target_future_info['ZZ1000'] = (target_future_info['ZZ1000'] + target_future_info['ZZ2000'])
    #     target_future_info.drop(['ZZ2000'], inplace=True)
    # ======    
    # print(target_future_info)
    # update_records_info(product_records, product_name, 'product_target_future_info',
    #     (product_records.get(product_name, {}).get('product_target_future_info', pd.Series()).add(target_future_info, fill_value=0)))

    try:
        product_future_hold_info = product_future_hold_df.loc[product_name]
    except KeyError:
        product_future_hold_info = pd.Series({'HS300': 0, 'ZZ500': 0, 'ZZ1000': 0})
    # print(product_future_hold_info)
    update_records_info(product_records, product_name, 'product_future_hold_info', product_future_hold_info)

    # ------


    account_future_value = get_account_future_value(account_name=account_name, date=pre_date)
    long_short_ratio = long_target_value / account_future_value if account_future_value != 0 else None
    update_records_info(account_records, account_name, 'long_short_ratio', long_short_ratio)
    
    # product_target_future_count[product_name] = product_target_future_count.get(product_name, 0) + target_future_num
    
    # update_records_info(account_records, account_name, 'target_future_num', target_future_num)
    # if account_name in kf_accounts_dict.keys():
        # product_target_future_count[kf_accounts_dict[account_name]['product_name']] = product_target_future_count.get(kf_accounts_dict[account_name]['product_name'], 0) + target_future_num
    # elif account_name in accounts_fj['accounts'].keys():
        # product_target_future_count[accounts_fj['accounts'][account_name]['product_name']] = product_target_future_count.get(accounts_fj['accounts'][account_name]['product_name'], 0) + target_future_num
        # product_target_future_count.update({accounts_fj['accounts'][account_name]['product_name'] : target_future_num})
    target = target[['symbol', 'target_shares', 'direction']]
    
    return target


def cal_changes(account_name, hold, target, trade_config):
    """
    hold:    symbol, hold_shares, direction, available_shares
    target:  symbol, target_shares, direction
    """
    changes = pd.concat([hold.set_index(['symbol', 'direction']), target.set_index(['symbol', 'direction'])], axis=1, join='outer')
    
    changes = changes.fillna(0)
    
    changes['order_shares'] = changes['target_shares'] - changes['hold_shares']
    changes = changes.reset_index()
    changes['order_shares_bf_adjust'] = changes['order_shares']
    changes = changes.apply(adjust_changes, axis=1)
    changes['order_shares'] = changes['order_shares'].astype(int)
    changes['symbol'] = changes['symbol'].astype(int)

    # check available_shares 是否大于 order_shares
    changes['cant_close_shares'] = changes['available_shares'] + changes['order_shares']
    mask_check_available = changes['cant_close_shares'] < 0
    if mask_check_available.any():
        cant_close = changes[mask_check_available].merge(pre_close[['symbol','close']], on='symbol', how='left')
        cant_close_value_long = (np.where(cant_close['direction']==1, cant_close['cant_close_shares'].abs(), 0) * cant_close['close']).sum()
        cant_close_value_short = (np.where(cant_close['direction']==-1, cant_close['cant_close_shares'].abs(), 0) * cant_close['close']).sum()
        logger.error(f'当前持仓可用不足: \n{changes[mask_check_available]}')
        logger.error(f'当前多头不能平仓: {cant_close_value_long:,.2f}, 空头不能平仓: {cant_close_value_short:,.2f}')

        changes.loc[mask_check_available, 'order_shares'] =  - (changes.loc[mask_check_available, 'available_shares']).abs()
    changes.drop(columns=['cant_close_shares'], inplace=True)
    # ------------------    

    changes = changes.set_index('symbol').merge(pre_close[['symbol','close']].set_index('symbol'), left_index=True, right_index=True, how='left')
    changes = changes.reset_index()
    
    
    # 过滤不交易股票
    # if account_name in ['test']:
    
    non_buy_stock_lst = non_buy_stock_lst_all_accounts.copy() + non_buy_stock_by_account.get(account_name, []).copy()
    non_sell_stock_lst = non_sell_stock_lst_all_accounts.copy() + non_sell_stock_by_account.get(account_name, []).copy()

    if len(non_buy_stock_lst) > 0 and changes['symbol'].isin(non_buy_stock_lst).any():
        logger.warning(f'\n过滤不买入股票: \n{account_name}\n     {changes[(changes["symbol"].isin(non_buy_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] > 0)]}\n\n')
        # changes = changes[~changes['symbol'].isin(non_buy_stock_lst)]
        changes.loc[(changes['symbol'].isin(non_buy_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] > 0), 'order_shares'] = 0
        
    if len(non_sell_stock_lst) > 0 and changes['symbol'].isin(non_sell_stock_lst).any():
        logger.warning(f'\n过滤不卖出股票: \n{account_name}\n     {changes[(changes["symbol"].isin(non_sell_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] < 0)]}\n\n')
        # changes = changes[~changes['symbol'].isin(non_sell_stock_lst)]
        changes.loc[(changes['symbol'].isin(non_sell_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] < 0), 'order_shares'] = 0
    
    # =========== 检查空头是否有反向交易
    # try get morning hold
    # if failed, use current hold and warns
    
    # hold - morning_hold
    # filter short hold direction
    # compare to changes direction
    
    # warn the t0 trades 
    # flag confirm to replace the t0 trades to 0    
    # morning_hold = get_stock_morning_hold(account_name)




    
    # ===========
    
    # num_filter_small_order_amount = trade_config.get('filter_small_order_amount', 0)
    # if num_filter_small_order_amount is not None and num_filter_small_order_amount > 0:
    #     changes['order_shares'] = np.where(changes['order_shares'].mul(changes['close'], axis=0).abs() < num_filter_small_order_amount,
    #                 0,
    #                 changes['order_shares']
    #     )
    # changes = changes.reset_index()
        
    # 记录 order info
    # mask_adjust = changes['order_shares'] != changes['order_shares_bf_adjust']
    # order_diffs = changes[mask_adjust].copy()
    # order_diffs['order_diffs'] = order_diffs['order_shares'] - order_diffs['order_shares_bf_adjust']
    # order_over_buy_shares = int(order_diffs[order_diffs['order_diffs'] > 0]['order_diffs'].sum())
    # order_over_sell_shares = int(order_diffs[order_diffs['order_diffs'] < 0]['order_diffs'].sum())
    # logger.warning('调整过的委托行数 :  {}'.format(len(order_diffs)))
    # logger.warning('调整后的少卖股数 :  {}'.format(order_over_buy_shares))
    # logger.warning('调整后的少买股数 :  {}'.format(order_over_sell_shares))
    
    changes['order_value'] = changes['order_shares'].mul(changes['close'], axis=0)
    
    changes['exchange'] = changes['symbol'].apply(symbol_to_exchange_cn)
    # SH_netValue = changes['order_value'][changes['exchange'] == '上交所'].sum()
    # SZ_netValue = changes['order_value'][changes['exchange'] == '深交所'].sum()
    trade_size = changes['order_value'].abs().sum()  # long & short
    changes_len = (changes['order_shares'] !=0).sum()

    long = changes[changes['direction'] == 1]
    short = changes[changes['direction'] == -1]
    
    
    
    long_buy_size = long[long['order_shares'] > 0]['order_value'].sum()
    long_sell_size = long[long['order_shares'] < 0]['order_value'].sum()
    long_order_net = long['order_value'].sum()
    
    short_buy_size = short[short['order_shares'] > 0]['order_value'].sum()
    short_sell_size = short[short['order_shares'] < 0]['order_value'].sum()
    short_order_net = short['order_value'].sum()
    

    long_buy_ratio = long_buy_size / get_records_info(account_records, account_name, 'long_hold_value')
    long_sell_ratio = long_sell_size / get_records_info(account_records, account_name, 'long_hold_value')
    
    short_buy_ratio = short_buy_size / get_records_info(account_records, account_name, 'short_hold_value')
    short_sell_ratio = short_sell_size / get_records_info(account_records, account_name, 'short_hold_value')
    # trade_size_ratio = trade_size / get_records_info(account_records, account_name, 'long_hold_value')
    
    
    
    # 如果要出计算过程, 可以打开下面的注释
    # write_file(changes, file_type='xls', dest_type='dav', dest_path='changes.xls', index=False)
    update_records_info(account_records, account_name, 'long_order_net', long_order_net)
    update_records_info(account_records, account_name, 'short_order_net', short_order_net)
    # update_records_info(account_records, account_name, 'SH_netValue', SH_netValue)
    # update_records_info(account_records, account_name, 'SZ_netValue', SZ_netValue)
    update_records_info(account_records, account_name, 'trade_size', trade_size)
    update_records_info(account_records, account_name, 'long_buy_size', long_buy_size)
    update_records_info(account_records, account_name, 'long_sell_size', long_sell_size)
    update_records_info(account_records, account_name, 'long_buy_ratio', long_buy_ratio)
    update_records_info(account_records, account_name, 'long_sell_ratio', long_sell_ratio)
    
    update_records_info(account_records, account_name, 'short_buy_size', short_buy_size)
    update_records_info(account_records, account_name, 'short_sell_size', short_sell_size)
    update_records_info(account_records, account_name, 'short_buy_ratio', short_buy_ratio)
    update_records_info(account_records, account_name, 'short_sell_ratio', short_sell_ratio)
    
    update_records_info(account_records, account_name, 'num', changes_len)
    # update_records_info(account_records, account_name, 'sell_shares_reduced', order_over_buy_shares)
    # update_records_info(account_records, account_name, 'buy_shares_reduced', order_over_sell_shares)
    product_name = trade_config.get('product_name')
    update_records_info(product_records, product_name, 'product_long_buy_size',
        (product_records.get(product_name, {}).get('product_long_buy_size', 0) + long_buy_size))
    update_records_info(product_records, product_name, 'product_long_sell_size',
        (product_records.get(product_name, {}).get('product_long_sell_size', 0) + long_sell_size))
    
    changes = changes[changes['order_shares'] != 0]
    return changes[['symbol', 'order_shares', 'direction']]


def update_records_info(records:dict, record_key, item_key, item_value):
    if record_key not in records:
        records[record_key] = {}
    records[record_key][item_key] = item_value

def get_records_info(records:dict, record_key, item_key):
    if record_key not in records:
        return None
    return records[record_key].get(item_key)

# def record_order_info(order_records, account_name):
#     pass

def adjust_trade_order_args(account_name, trade_order_args, account_config):
    # if trade_order_args.get('order_type') == 'zhongxin_cats':
    #     trade_order_args.update({'hold': hold, 'target': target})
    if trade_order_args.get('order_type') != 'xt1' and trade_order_args.get('order_type') is not None:
        time_interval = account_config.get('time_interval') if account_config.get('time_interval') is not None else DEFAULT_TRADE_INTERVAL_MINS
        start_time = account_config.get('start_time')
        end_time = account_config.get('end_time')
        nowtime = datetime.datetime.now()
        
        # 开始时间, 结束时间同时存在, 优先
        if start_time is not None and end_time is not None:
            trade_order_args.update({'start_time': start_time.zfill(4)+'00', 'end_time': end_time.zfill(4)+'00'})
            
        # 开始时间存在, 结束时间不存在, 用时间间隔
        elif start_time is not None:
            end_time = delta_endtime(time_interval, start=start_time.zfill(4))
            trade_order_args.update({'start_time': start_time.zfill(4)+'00', 'end_time': end_time}) 
            
        # 开始时间不存在, 结束时间存在, 开始时间用当前时间
        elif end_time is not None:
            if nowtime < datetime.datetime.strptime(date+'130000', '%Y%m%d%H%M%S') and nowtime > datetime.datetime.strptime(date+'113000', '%Y%m%d%H%M%S'):
                start_time = '130000'
            else:
                start_time = nowtime.strftime('%H%M00')

            trade_order_args.update({'start_time': start_time, 'end_time': end_time.zfill(4)+'00'})
            
        # 开始时间, 结束时间都不存在, 只能用时间间隔
        else: # interval 默认 30 min
            # if now time < 9:30, use 9:30
            # nowtime = datetime.datetime.now()
            if nowtime < datetime.datetime.strptime(date+'093000', '%Y%m%d%H%M%S'):
                start_time = '093000'
            elif nowtime < datetime.datetime.strptime(date+'130000', '%Y%m%d%H%M%S') and nowtime > datetime.datetime.strptime(date+'113000', '%Y%m%d%H%M%S'):
                start_time = '130000'
            else:
                start_time = nowtime.strftime('%H%M00')
                # nowtime = datetime.datetime.strptime(date+'093000', '%Y%m%d%H%M%S')
            # now_hms = nowtime.strftime('%H%M')
            # time_interval = datetime.timedelta(minutes=time_interval)
            end_time = delta_endtime(time_interval, start=start_time.zfill(6)[:4])
            
            # 参与集合竞价的账户
            # if trade_order_args.get('order_type') in ['gtrade', 'yuliang_scan']:
            #     start_time = '091500'
            trade_order_args.update({'start_time': start_time, 'end_time': end_time}) 
    return trade_order_args

def adjust_account_config(account_name, account_config):
    # arg: order_file_suffix
    if account_config.get('order_file_suffix') is None:
        account_config['order_file_suffix'] = ''
        
    # arg: sw_dest_dir
    if account_config.get('sw_dest_dir') is None:
        account_config['sw_dest_dir'] = ''
    
    # arg: focus
    if account_config.get('exclude') is None:
        account_config['exclude'] = False
    else:
        account_config['exclude'] = True    
    return account_config


def get_available_fund(account_name, date, account_config):
    # print(date)
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    if account_name in zs_accounts_dict:

        # acc_fig = kf_accounts_dict[account_name]
        try:
            # if acc_fig.get('account_file') and acc_fig.get('account_type') in ['stockaccount', 'marginaccount']:
            #     account_df = read_remote_file(os.path.join(account_name, 'account', acc_fig['account_file']['file_name'].format(pre_date)),
            #                                 src_type=acc_fig['account_file']['file_src'])
            # account_file = 'marginaccount_{}.xls' if acc_fig['account_type'] == 'marginaccount' else 'stockaccount_{}.xls'
            # find latest account info file name
            
            
            accountinfo_file = f'accountinfo_{date}.xls'
            
            account_file_src = 'zsdav'
            account_df = read_remote_file(os.path.join(account_name, 'account', accountinfo_file.format(date)),
                                            src_type=account_file_src)
            if account_df.shape[0] > 0:
                available_margin = account_df['available_margin'].values[0] if 'available_margin' in account_df else 0
                available_fund = account_df['available_fund'].values[0] if 'available_fund' in account_df else 0
                available_fund = available_margin * 4 if available_fund == 0 else available_fund
            else:
                available_fund = 0
            # else:
            #     available_fund = 0
        except FileNotFoundError:        
            available_fund = 0
            
            
    else:
        # available_fund = get_available_fund_from_pnl_file_fj(account_name, pre_date, account_config)
        available_fund = 0
        
        # get_available_fund_from_pnl_file(account_name, pre_date, account_config)
    return available_fund

def check_stock_weight(target, close):
    # target = target.rename(columns={'target_shares': 'volume'}).copy()
    

    stock_ratio = target.merge(close, on='symbol', how='left')
    stock_ratio['stock_value'] = stock_ratio['target_shares'] * stock_ratio['close']

    long_side = stock_ratio[stock_ratio['target_shares'] > 0]
    short_side = stock_ratio[stock_ratio['target_shares'] < 0]

    stock_ratio.loc[long_side.index, 'weight'] = long_side['stock_value'].div( long_side['stock_value'].sum() )
    stock_ratio.loc[short_side.index, 'weight'] = short_side['stock_value'].div( short_side['stock_value'].sum() )

    stock_ratio = stock_ratio.sort_values('weight', ascending=False)

    stock_ratio_filterd = stock_ratio[stock_ratio['weight'] > 0.047]
    
    if not stock_ratio_filterd.empty:
        logger.warning(f'集中度:\n{stock_ratio_filterd}')

def check_intraday_direction_deal(account_name, hold, target, morning_hold):
    '''
    检查记录今日账户已成交情况, 基于hold, morning_hold
    检查是否有融券方向交易日内反向, 基于hold与morning_hold, 是否与target - hold 的方向相同
    '''
    dealt = hold.set_index(['symbol', 'direction'])['hold_shares'].sub(morning_hold.set_index(['symbol', 'direction'])['hold_shares'], fill_value=0).to_frame('dealt_shares')
    
    dealt = dealt.reset_index().merge(pre_close[['symbol','close']], on='symbol', how='left')
    
    dealt_long_open = dealt[(dealt['direction'] == 1) & (dealt['dealt_shares'] > 0)]
    dealt_long_close = dealt[(dealt['direction'] == 1) & (dealt['dealt_shares'] < 0)]
    dealt_short_open = dealt[(dealt['direction'] == -1) & (dealt['dealt_shares'] > 0)]
    dealt_short_close = dealt[(dealt['direction'] == -1) & (dealt['dealt_shares'] < 0)]


# def get_available_fund_from_pnl_file(account_name, date, account_config):
#     splited_res = account_name.rsplit('_', 1)
#     if len(splited_res) == 2:
#         product_name = splited_res[0]
#         broker = splited_res[1]
#     else:
#         product_name = splited_res[0]
#         broker = ''

#     pnl_path = os.path.join(account_name, 'account', account_config.get('pnl_file'))
#     try:
#         df = read_remote_file(pnl_path, src_type='inner', sheet_name=product_name, header=[1,2], index_col=0)
#         for cols in df.columns:
#             if '股票端' in cols[0] and broker in cols[0]:
#                 col_1 = cols[0]
#                 break
#         fund = df.loc[date, (col_1, '可用资金')]
#     except Exception as e:
#         logger.error(e)
#         fund = 0
#     return fund

# def update_future_num_to_records(account_records:pd.DataFrame, product_future_target_count):
#     account_records['target_future_num'] = account_records.index.map(lambda x: product_target_future_count[accounts_config[account_name]['product']])

def deal_display_orders_info(account_records, accs:list='all'):
    if len(account_records) == 0:
        print('没有账户交易单记录')
        return
    
    # pprint(account_records)
    order_info = pd.DataFrame.from_dict(account_records, orient='index').round(4)
    if accs != 'all':
        if isinstance(accs, str):
            accs = [accs]
        
        order_info = order_info.loc[accs]
    # print(order_info)

    # add product info
    order_info['target_future_num'] = order_info.index.map(lambda x: 
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_target_future_count'))
    
    # -------------------------------
    # order_info['product_target_future_info'] = order_info.index.map(lambda x: 
    #     get_records_info(product_records, record_key=accounts_config[x]['product_name'],
    #                      item_key='product_target_future_info'))
    order_info['product_future_hold_info'] = order_info.index.map(lambda x: 
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_future_hold_info'))
    
    # print(order_info['product_target_future_info'])
    # print(order_info['product_future_hold_info'])
    
    order_info['product_future_info'] = order_info['product_future_hold_info']
    
    def format_future_info(ds):
        msg = []
        for idx in ds.index:
            if ds.loc[idx] != 0 and pd.notna(ds.loc[idx]) and abs(ds.loc[idx]) > 0.3:
                msg.append(f'{idx}: {ds.loc[idx]:.1f}')
        msg = ' '.join(msg)
        return msg    
    order_info['product_future_info'] = order_info['product_future_info'].map(lambda x: format_future_info(x))
    # -------------------------------
        
    order_info['product_long_hold_value'] = order_info.index.map(lambda x:
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_long_hold_value'))
    order_info['product_target_value'] = order_info.index.map(lambda x:
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_target_value'))
    order_info['product_long_buy_size'] = order_info.index.map(lambda x:
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_long_buy_size'))
    order_info['product_long_sell_size'] = order_info.index.map(lambda x:
        get_records_info(product_records, record_key=accounts_config[x]['product_name'],
                         item_key='product_long_sell_size'))
    
    order_info['product_buy_ratio'] = order_info['product_long_buy_size'] / order_info['product_long_hold_value']
    order_info['product_sell_ratio'] = order_info['product_long_sell_size'] / order_info['product_long_hold_value']
    order_info['product_long_net_buy'] = order_info['product_long_buy_size'] + order_info['product_long_sell_size']
                                                                        
        
        # product_records[accounts_config[x]['product_name']]['product_target_future_count'])

    order_info = order_info.reset_index(names=['account_name']).sort_values(['product_name', 'account_name'], ascending=True).set_index('account_name')
    order_info['start_time'] = order_info['start_time'].apply(lambda x: x[0:4] if type(x)==str and len(x)==6 else x)
    order_info['end_time'] = order_info['end_time'].apply(lambda x: x[0:4] if type(x)==str and len(x)==6 else x)

    # 调整显示格式
    # 如果目标仓位大于期货手数1手, 或者小于目标仓位(裸空了), 要显示
    # order_info['future_trades'] = (order_info['target_future_num'] - order_info['hold_future_num'].abs()).map(lambda x: -(round(x, 2)) if (x>1 or x<-0.05) else '')
    
    
    
    # 金额显示 万
    order_info['资金不足'] = (order_info['available_fund'] - order_info['long_order_net']).apply(lambda x: 
        f'{x/10000:,.2f} 万' if x < 0 else '') 
    for col in ['long_hold_value', 'target_value', 'long_order_net', 'trade_size', 'long_buy_size', 'long_sell_size', 'available_fund', 
                'product_long_hold_value', 'product_target_value', 'product_long_buy_size', 'product_long_sell_size', 'product_long_net_buy',
                'short_hold_value', 'short_target_value', 'short_buy_size', 'short_sell_size',
                ]:
        # order_info[col] = order_info[col].apply(lambda x: f'{x/10000:,.2f} 万' if (x !='' and x !=0) else '')
        # TODO
        order_info[col] = order_info[col].apply(lambda x: f'{x/10000:,.2f} 万' if x !='' else '')
        
    # 百分% 调整
    for col in ['long_buy_ratio', 'long_sell_ratio', 'product_buy_ratio', 'product_sell_ratio', 'short_buy_ratio', 'short_sell_ratio']:
        # print(order_info[col])
        order_info[col] = order_info[col].apply(lambda x: f'{x*100:,.2f}' if x !='' and pd.notna(x) and not np.isinf(x) else '')
        # order_info[col] = order_info[col].replace(np.nan, '')
 
    # for col in ['short_hold_value', 'short_target_value']:
    #     order_info[col] = order_info[col].apply(lambda x: f'{x/10000:,.2f} 万' if x !=0 else '')
    
    # 多空比例显示调整, 只显示大于1.2或小于0.8的
    order_info['long_short_ratio'] = order_info['long_short_ratio'].apply(
        lambda x: f'{x:,.2f}' if not( x is None or np.isinf(x)) and (x>1.18 or x<0.82) else '')
    
    # 期货交易手数调整
    # for col in ['hold_future_num', 'target_future_num', 'future_trades']:
    #     order_info[col] = order_info[col].apply(lambda x: f'{x:,.2f}' if x !='' else '')
        
    # 去重复
    # order_info.loc[(order_info.index.isin(duotou_accounts)), ['future_trades', 'product_future_info']] = '指增'
    # order_info.loc[ order_info.duplicated(subset=['hold_future_num', 'target_future_num', 'future_trades']), 
    #                ['hold_future_num', 'target_future_num', 'future_trades', 
    #                 'product_long_hold_value', 'product_target_value', 'product_long_buy_size',
    #                 'product_long_sell_size', 'product_buy_ratio', 'product_sell_ratio',
    #                 'product_long_net_buy', 'product_future_info',
    #                 ]] = ''
    
    order_info = order_info.rename(columns={
        'long_order_net': '多头净买',
        # 'SH_netValue': '上交所净额',
        # 'SZ_netValue': '深交所净额',
        'trade_size'                  : '交易总额',
        'long_buy_size'               : '多头买额',
        'long_sell_size'              : '多头卖额',
        'long_buy_ratio'              : '多头买 %',
        'long_sell_ratio'             : '多头卖 %',
        'num'                         : '母单数',
        'sell_shares_reduced'         : '少卖股数',
        'buy_shares_reduced'          : '少买股数',
        'long_hold_value'             : '多头市值',
        'target_value'                : '多头目标',
        'available_fund'              : '可用资金',
        'start_time'                  : 'Start',
        'end_time'                    : 'End',
        'order_file_suffix'           : 'suffix',
        # 'hold_future_num'             : '对应期货手数',
        # 'target_future_num'           : '目标手数',
        # 'future_trades'               : '期货交易',
        'product_future_info'         : '目标对比期货持仓',
        'product_long_hold_value'    : '产品多头市值',
        'product_target_value'       : '产品目标市值',
        'product_long_buy_size'  : '产品多头买额',
        'product_long_sell_size' : '产品多头卖额',
        'product_buy_ratio' : '产品多头买 %',
        'product_sell_ratio' : '产品多头卖 %',
        'product_long_net_buy' : '产品多头净买',
        'long_short_ratio' : '多空比',
        'short_hold_value' : '空头市值',
        'short_target_value' : '空头目标',
        'short_buy_size' : '空头开仓',
        'short_sell_size' : '空头平仓',
        'short_buy_ratio' : '空头开 %',
        'short_sell_ratio' : '空头平 %',
        'short_order_net' : '空头净额',
    })
    col_1 = [
            '交易员',
            '多头市值',
            '多头目标',
            '多头买额',
            '多头卖额',
            '多头净买',
            '多头买 %',
            '多头卖 %',
            '可用资金',

            '产品多头市值',
            '产品多头净买',
            '产品多头买 %',
            '产品多头卖 %',
            # '对应期货手数',
            # '目标手数',
            # '期货交易',
            '目标对比期货持仓',
            '资金不足',
    ]
    col_2 = [
            '多头市值',
            '空头市值',
            '多头目标',
            '空头目标',
            '多头买额',
            '多头卖额',
            '空头开仓',
            '空头平仓',
            '多头买 %',
            '多头卖 %',
            '空头开 %',
            '空头平 %',
            
    ]
    col_3 = [
            'Start',
            'End',
            '母单数',
            'suffix'
    ]
    order_info = order_info[col_1 + col_3]

    order_info.index.name = 'Account'
    # order_info.index = order_info.index.map(lambda x: f'{x:<18s}')
    # display_split = -4
    
    sep_1 = order_info.columns.get_loc(col_3[0])
    # sep_2 = order_info.columns.get_loc(col_3[0])
    
    
    # logger.info('交易单信息: \n\n {}'.format(
    print(f' {date} 交易单信息:')
    # 最终只显示 2 位小数
    print(tabulate(
        order_info.iloc[:,:sep_1].reset_index(), headers='keys', floatfmt=",.2f", numalign="right", stralign="right", showindex=False,
        colalign=['left']  # for first col
    ))
    # print('\n')
    # print(tabulate(
    #     order_info.iloc[:,sep_1:sep_2].reset_index(), headers='keys', floatfmt=",.2f", numalign="right", stralign="right", showindex=False,
    #     colalign=['left']  # for first col
    # ))
    
    # logger.info('交易单信息: \n\n {}'.format(
    print('\n')
    print(tabulate(
        order_info.iloc[:,sep_1:].reset_index(), headers='keys', floatfmt=",.2f", numalign="right", stralign="right", showindex=False,
        colalign=['left']  # for first col
    ))
    # logger.info('交易单信息: \n')
    # logger.info('{}'.format(order_info.to_string()))

def update_order_time(account_name, trade_order_args):
        update_records_info(account_records, account_name, 'start_time', trade_order_args.get('start_time'))
        update_records_info(account_records, account_name, 'end_time', trade_order_args.get('end_time'))

def check_trades_some_ticker(account_name, changes, some_ticker:list, res_df:pd.DataFrame):
    if some_ticker is None:
        return res_df
    
    if len(some_ticker) == 0:
        return res_df

    if some_ticker == ['all']:
        some_ticker = changes['symbol'].unique().tolist()
    
    if isinstance(some_ticker, str):
        some_ticker = [some_ticker]
    some_ticker = some_ticker + [int(x) for x in some_ticker]
    
    
    # check some ticker
    if changes['symbol'].isin(some_ticker).any():
        tmp_df = changes[changes['symbol'].isin(some_ticker)].copy()
        tmp_df.insert(0, 'account_name', account_name)
        res_df = pd.concat([res_df, tmp_df], ignore_index=True)
        
    return res_df


def send_order(order_records, accounts_config):
    num_dav_file_sent = 0
    num_gt_file_sent = 0
    for account_name in order_records:
        local_order_files = order_records.get(account_name, [])
        flag_gt_order = accounts_config[account_name]['trade_config'].get('gt_order', False)
        flag_zzw_xt_order = accounts_config[account_name]['trade_config'].get('gt_zzw', False)
        dest_type = accounts_config[account_name]['trade_config'].get('dest_type')
        dest_path_prefix = accounts_config[account_name]['trade_config'].get('dest_path_prefix')
        # print(account_name)
        # print(dest_type)
        # print(dest_path_prefix)
        
        for local_order_file in local_order_files:
            file_name = os.path.basename(local_order_file)

            # 如果有dest_type 和 dest_path_prefix , 优先发送
            # if dest_type is not None and dest_path_prefix is not None:
            #     sftp_clent_outer.put(local_order_file,
            #                     os.path.join(dest_path_prefix, account_name, 'order', file_name)
            #                     )
            #     logger.info('outer ftp order file sent: {}'.format(file_name))
            
            # else:
            sftp_clent_zsdav.put(local_order_file,
                            os.path.join(account_name, 'order', file_name)
                            )
            logger.info('dav order file sent: {}'.format(file_name))
            num_dav_file_sent += 1
            
            # 是否发送gt, 如果文件dataframe不为空, 发送gt
            # if flag_gt_order == True and read_file(local_order_file).shape[0] > 0:
            #     num_gt_file_sent += send_order_to_gt_ftp(account_name=account_name, local_order_file=local_order_file)
            
            # addition
            # if flag_zzw_xt_order is not None and pd.notna(flag_zzw_xt_order):
            #     # sftp_clent_dav.put(local_order_file,
            #     #                     os.path.join('迅投实盘交易导入', 'zzw', file_name)
            #     #                 )
            #     sftp_clent_dav.put(local_order_file,
            #                         os.path.join('迅投实盘交易导入', str(flag_zzw_xt_order), file_name)
            #                     )
            # 委托单备份
            # send_order_to_kf_ftp_backup(account_name=account_name, local_order_file=local_order_file)
    
    
    import re
    def extract_number(s):
        pattern = r'order(_(\d+))?\.csv'
        match = re.search(pattern, s)
        if match:
            number = match.group(2)
            return number if number else ''
        return ''
    gt_suffix = extract_number(local_order_file)
    
    logger.info('dav/outer order files sent: {}'.format(num_dav_file_sent))
    logger.info('gt order files sent       : {}'.format(num_gt_file_sent))
    logger.info(f'gt 交易 {num_gt_file_sent} 个账户, 后缀 {gt_suffix}' if gt_suffix != '' else f'gt 交易 {num_gt_file_sent} 个账户, 无后缀')

# def send_order_to_gt_ftp(account_name, local_order_file):
#     # check if dest folder exists
#     gt_api_dirs = sftp_clent_gtapi.listdir('')
#     if account_name not in gt_api_dirs:
#         sftp_clent_gtapi.mkdir(account_name)
#     if 'order' not in sftp_clent_gtapi.listdir(account_name):
#         sftp_clent_gtapi.mkdir(os.path.join(account_name, 'order'))
    
#     # send order    
#     file_name = os.path.basename(local_order_file)
#     try:
#         sftp_clent_gtapi.put(local_order_file,
#                             os.path.join(account_name, 'order', file_name)
#                             )
#         logger.info('gt order file sent: {}'.format(file_name))
#         res = 1
#     except Exception as e:
#         logger.error(e)
#         res = 0

    
        
#     return res

# def send_order_to_kf_ftp_backup(account_name, local_order_file):
#     # check if dest folder exists
#     dirs = sftp_clent_outer.listdir('kf')
#     if account_name not in dirs:
#         sftp_clent_outer.mkdir(os.path.join('kf',account_name))
#     if 'order' not in sftp_clent_outer.listdir(os.path.join('kf', account_name)):
#         sftp_clent_outer.mkdir(os.path.join('kf', account_name, 'order'))
    
#     # send order    
#     file_name = os.path.basename(local_order_file)
#     try:
#         sftp_clent_outer.put(local_order_file,
#                             os.path.join('kf', account_name, 'order', file_name)
#                             )
#         # logger.info('order file sent kf ftp: {}'.format(file_name))
#         res = 1
#     except Exception as e:
#         logger.error(e)
#         res = 0
#     return res


# def get_sweeporder_log() -> dict:
#     # 如果 'SweepOrder_{date}.xlsx' 存在目录中, 读取文件, 否则创建对应日期的新文件
#     if os.path.exists(sweep_order_file):
#         sweep_order_log = pd.read_csv(sweep_order_file, index_col=0)['Sent']
#         sweep_order_log = sweep_order_log.to_dict()
#     else:
#         sweep_order_log = {}
#     return sweep_order_log

# def save_sweeporder_log(sweep_order_log):
#     # 保存log
#     sweep_order_log = pd.Series(sweep_order_log).to_frame('Sent')
#     sweep_order_log.to_csv(sweep_order_file)


def send_sweeporder_files(order_records, accounts_config):
    sftp_methods = {
        'zxdav'    : sftp_clent_zsdav,
        # 'trade'  : sftp_clent_trade,
        # 'trade2' : sftp_clent_trade2,
        # # 'aws'    : sftp_clent_aws,
        # 'work'   : sftp_clent_work,
    }
    for account_name in order_records:
        local_order_files = order_records.get(account_name, [])
        flag_sw_order = accounts_config[account_name]['trade_config'].get('sweeporder', False)
        sw_dest_type = accounts_config[account_name]['trade_config'].get('sw_dest_type')
        sw_dest_dir = accounts_config[account_name]['trade_config'].get('sw_dest_dir')
        dbf_type = accounts_config[account_name]['trade_config'].get('is_dbf')
        dbf_scan_file = accounts_config[account_name]['trade_config'].get('dbf_scan_file')
        dbf_header_type = accounts_config[account_name]['trade_config'].get('dbf_header')
        
        if flag_sw_order != True:
            continue

        # sweep_order_log = get_sweeporder_log()
        # if sweep_order_log.get(account_name, False) == True:
        #     logger.warning(f'{account_name} 账户已发送扫单, 不再重复发送.')
        #     continue
        
        # # 再次确认订单信息
        # deal_display_orders_info(account_records=account_records, accs=account_name)
        # flag_send_sw_order = input('\n        将发扫单, 是否确认? (confirm/....)\n\n                             ')
        
        # if flag_send_sw_order != 'confirm':
        #     logger.warning(f'{account_name} 账户未发送扫单.')
        #     continue
        # 实际发送扫单
        try:
            if dbf_type in ['kf_dbf', 'yuliang_dbf']:        # 卡方 dbf 文件, 宇量 dbf 文件
                dbf_scan_file = dbf_scan_file.format(date=date)
                dbf_scan_path = os.path.join(sw_dest_dir, dbf_scan_file)
                for local_order_file in local_order_files:
                    appending_df = read_file(local_order_file, dtype={
                        'ExternalId' : 'str',
                        'ClientName' : 'str',
                        'Symbol'     : 'str',
                        'Side'       : 'int',
                        'OrderQty'   : 'int',
                        'OrdType'    : 'int',
                        'EffTime'    : 'str',
                        'ExpTime'    : 'str',
                        'LimAction'  : 'int',
                        'AftAction'  : 'int',
                        'AlgoParam'  : 'str',
                        'UnitId'     : 'str',
                        'ProductId'  : 'str',
                        
                        'EXTERNALID' : 'str',
                        'CLIENTNAME' : 'str',
                        'SYMBOL'     : 'str',
                        'SIDE'       : 'int',
                        'ORDERQTY'   : 'int',
                        'ORDTYPE'    : 'int',
                        'EFFTIME'    : 'str',
                        'EXPTIME'    : 'str',
                        'LIMACTION'  : 'int',
                        'AFTACTION'  : 'int',
                        'ALGOPARAM'  : 'str',
                        'STOCKNUM'   : 'int',
                        'SHAREHOLD'  : 'str',
                        'CLIENTID'   : 'str',
                        'FUNDID'     : 'str',
                        'STRCODE'    : 'str',
                    })
                    # lower df columns
                    appending_df.columns = [c.lower() for c in appending_df.columns]

                    # 保证可选字段空值为 ''
                    for col in appending_df.columns:
                        if col.lower() in ['clientname', 'unitid', 'productid', 'sharehold', 'clientid', 'fundid', 'command_id', 'inserttime']:
                            appending_df[col].fillna('', inplace=True)        
                    # print(appending_df)
                    
                    ret =append_dbf(appending_df, dest_type=sw_dest_type, dest_path=dbf_scan_path, dbf_fields_list=VARS.dbf_files_header_dict.get(dbf_header_type))
                    if ret == False:
                        logger.error('dbf order file append failed: {}'.format(dbf_scan_path))
                        continue
                    logger.info('dbf order file append: {}'.format(dbf_scan_path))
                    time.sleep(1.0)
                # sweep_order_log[account_name] = sweep_order_log.get(account_name, 0) + 1
                # save_sweeporder_log(sweep_order_log)

            elif dbf_type in ['xuntou_dbf']:
                dbf_scan_file = dbf_scan_file.format(date=date).replace(date, f'{date}_{datetime.datetime.now().strftime("%H%M%S%f")}')
                dbf_scan_path = os.path.join(sw_dest_dir, dbf_scan_file)
                for local_order_file in local_order_files:
                    appending_df = read_file(local_order_file, dtype={
                        'order_type' : 'str',
                        'stock_code' : 'str',
                        'price_type' : 'str',
                        'act_type'   : 'str',
                        'brokertype' : 'str',
                        'volume'     : 'str',
                        'account_id' : 'str',
                        'tradeparam'  : 'str',
                        'command_id' : 'str',
                        'inserttime' : 'str',
                    })
                    # lower df columns
                    appending_df.columns = [c.lower() for c in appending_df.columns]

                    # 保证可选字段空值为 ''
                    for col in appending_df.columns:
                        if col.lower() in ['clientname', 'unitid', 'productid', 'sharehold', 'clientid', 'fundid', 'command_id', 'inserttime']:
                            appending_df[col].fillna('', inplace=True)        
                    # print(appending_df)
                    
                    ret =append_dbf(appending_df, dest_type=sw_dest_type, dest_path=dbf_scan_path, dbf_fields_list=VARS.dbf_files_header_dict.get(dbf_header_type))
                    if ret == False:
                        logger.error('dbf order file append failed: {}'.format(dbf_scan_path))
                        continue
                    logger.info('dbf order file append: {}'.format(dbf_scan_path))
                    time.sleep(1.0)
                # sweep_order_log[account_name] = sweep_order_log.get(account_name, 0) + 1
                # save_sweeporder_log(sweep_order_log)


            elif dbf_type in ['hx_csv']:
                dbf_scan_file = dbf_scan_file.format(date=date)
                dbf_scan_path = os.path.join(sw_dest_dir, dbf_scan_file)
                for local_order_file in local_order_files:
                    appending_df = read_file(local_order_file, dtype={
                        'account_id' : 'str',
                        'start_time' : 'str',
                        'end_time'   : 'str',
                        'trade_type' : 'int',
                        'limit_up_down_trading' : 'int',
                    })
                    # 保证可选字段空值为 ''
                    for col in appending_df.columns:
                        if col in ['algo_params']:
                            appending_df[col].fillna('', inplace=True)        

                    ret =append_csv(appending_df, dest_type=sw_dest_type, dest_path=dbf_scan_path)
                    if ret == False:
                        logger.error('dbf order file append failed: {}'.format(dbf_scan_path))
                        continue
                    logger.info('csv scan order file append: {}'.format(dbf_scan_path))
                    time.sleep(1.0)
                # sweep_order_log[account_name] = sweep_order_log.get(account_name, 0) + 1
                # save_sweeporder_log(sweep_order_log)

            else:                           # 其他扫单文件
                for local_order_file in local_order_files:
                    file_name = os.path.basename(local_order_file)
                    sftp_methods[sw_dest_type].put(local_order_file,
                                    os.path.join(sw_dest_dir, file_name),
                                    confirm=False
                                    )
                    logger.info('sweep order file sent: {}'.format(os.path.join(sw_dest_dir, file_name)))
                    time.sleep(1.0)
                # 添加发送记录
                # sweep_order_log[account_name] = sweep_order_log.get(account_name, 0) + 1
                # # 保存log
                # save_sweeporder_log(sweep_order_log)
        except Exception as e:
            logger.error(e)
            logger.warning(f'{account_name} 账户没有成功发送扫单.')
            continue


# def upload_position_gt_ftp(account_name, date):
#     # check if dest folder exists
#     gt_api_dirs = sftp_clent_gtapi.listdir('')
#     if account_name not in gt_api_dirs:
#         sftp_clent_gtapi.mkdir(account_name)
#     if 'position' not in sftp_clent_gtapi.listdir(account_name):
#         sftp_clent_gtapi.mkdir(os.path.join(account_name, 'position'))
    
#     # sftp_type = get_account_sftp(account_name=account_name)['ftp_type']
#     sftp_method = get_account_sftp(account_name=account_name)['sftp']

#     file_name = find_latest_remote_file_on_date(sftp_method=sftp_method,
#                                     dir=os.path.join(account_name, 'position'),
#                                     file_prefix='position.',
#                                     date=date
#                                     )
#     dest_file_name = f'position.{date}.csv'
    
#     file_path = os.path.join(account_name, 'position', file_name)
#     dest_file_path = os.path.join(account_name, 'position', dest_file_name)

#     with tempfile.NamedTemporaryFile(delete=True, suffix='.csv') as tmp:
#         # get csv file from remote
#         sftp_method.get(file_path, tmp.name)

#         # 回传远程文件
#         sftp_clent_gtapi.put(tmp.name, dest_file_path)
        
#         return True

# def update_future_num_info(account_name, account_config, future_num_df):
#     simple_product_name = account_name.split('_')[0]
#     fullname = product_account_dict[simple_product_name].get('db_fullname')
#     future_num = future_num_df.loc[fullname, 'volume']
#     pass


# def get_account_future_num(account_name, conn):
#     query="""
#     SELECT * FROM short_position_monitor_sa
#     WHERE
#     sub_account REGEXP '{}(?:_子[0-9])?$'
#     """.format(account_name)
#     df = pd.read_sql(query, conn)
#     return round(df['volume'].sum(),2)

def get_product_future_num(product_name, date):
    future_hold_file = os.path.join(FUTURE_HOLD_DIR, 'hold', f'hold_{date}.xls')
    df = read_remote_file(future_hold_file, src_type='zsdav')
    df['direction'] = df['longshort'].map(lambda x: 1 if x=='空头' else -1)
    df['volume'] = df['volume']*df['direction']
    
    futture_volume = df[df['product_name'] == product_name]['volume'].sum()
    return futture_volume

# def get_product_future_df(date):
#     future_hold_file = os.path.join(FUTURE_HOLD_DIR, 'hold', f'hold_{date}.xls')
#     df = read_remote_file(future_hold_file, src_type='dav')
#     def map_values(value):
#         if 'IC' in value or '中证500' in value:
#             return 'ZZ500'
#         elif 'IM' in value or '中证1000' in value:
#             return 'ZZ1000'
#         elif 'IF' in value or '沪深300' in value:
#             return 'HS300'
#         elif 'IH' in value or '上证50' in value:
#             return 'SZ50'
#         else:
#             return value  # 保留其他值不变
#     df['ticker'] = df['ticker'].map(map_values)

#     df = df[['product_name', 'ticker', 'volume']].groupby(['product_name', 'ticker']).sum().reset_index()
#     return df

def get_account_future_value(account_name, date):
    future_hold_file = os.path.join(FUTURE_HOLD_DIR, 'hold', f'hold_{date}.xls')
    df = read_remote_file(future_hold_file, src_type='zsdav')
    account_mask = df['account_name'] == account_name
    future_value = (df[account_mask]['volume'] * df[account_mask]['multiplier'] * df[account_mask]['close']).sum()
    return future_value


def get_trade_db_conn():
    host="**************"
    port=3306
    user="trade"
    password="trade@KF2023"
    database="trade"
    engine=create_engine('mysql+pymysql://{}:{}@{}:{}/{}'.format(user,urlquote(password),host,port,database))
    conn = engine.connect()
    return conn

# def get_recall_num(account_name, date):
#     try:
#         recall_file = find_latest_remote_file_on_date(
#             sftp_method=sftp_clent_dav,
#             dir=os.path.join(account_name, 'account'),
#             file_prefix='recall_',
#             date=date
#         )
#         df = read_remote_file(os.path.join(account_name, 'account', recall_file), src_type='dav')
#     except FileNotFoundError:
#         logger.warning(f'{account_name} {date} 未找到召回文件')
#         df = pd.DataFrame(columns=['INST_CODE', 'ORDER_COUNT', 'TOTAL_DEAL_COUNT', 'TRADE_TYPE', 'ORDER_STATUS'])
#     df = df.rename(columns={
#         'INST_CODE' : 'symbol',
#         'ORDER_COUNT' : 'recall_order_volume',
#         'TOTAL_DEAL_COUNT' : 'recall_deal_volume',
#         'TRADE_TYPE' : 'trade_type',
#         'ORDER_STATUS' : 'order_status',
#     })[['symbol', 'recall_order_volume', 'recall_deal_volume', 'trade_type', 'order_status']]
#     # sum up by symbol
#     '''
#     代码值说明
#     -1  待审核
#     -2  失败
#     0  待报
#     1  已报
#     2  部成
#     3  全成
#     4  部撤
#     5  全撤
#     6  待撤
#     '''
#     # 过滤掉已结束召回单
#     df = df[df['order_status'].isin([-1, 0, 1, 2, '-1', '0', '1', '2'])]
#     df = df.groupby(['symbol', 'trade_type']).sum().reset_index()
#     return df

# def calcu_future_num(product_name, date):
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     pass

# ==============================================================================
# ==============================================================================

local_order_timetag = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
local_account_dir = 'accounts'
local_account_orders_dir = os.path.join(local_account_dir, 'orders', local_order_timetag)
local_sweeporder_log_dir = os.path.join(local_account_dir, 'sweeporder')
sweep_order_file = os.path.join(local_sweeporder_log_dir, 'SweepOrder_{}.csv'.format(datetime.datetime.now().strftime('%Y%m%d')))
os.mkdir(local_account_orders_dir)

trade_config_file = 'others/trade_config.xlsx'

date = str(datetime.datetime.now().strftime('%Y%m%d'))

parser = argparse.ArgumentParser(description='generate special orders for non standard clients ...')
parser.add_argument('-d', '--date', help='order date.', required=False, default=date)
# parser.add_argument('-c', '--config', help='account configs, sep by ","', required=True)
# parser.add_argument('-dest', '--destination', help='send order location', required=False, default='no', choices=['dav', 'gt', 'no'])
parser.add_argument('-nosend', '--nosend', help='flag for sending order, -nosend for not sending', action='store_true')
parser.add_argument('-exc', '--toggleexclude', help='toggle for accounts excluded in config', action='store_false')
parser.add_argument('-f', '--flag', help='filter flags in config', required=False, default='all')
parser.add_argument('-all', '--allaccounts', help='toggle for all accounts selection', action='store_true')
parser.add_argument('-c', '--checkticker', help='check some ticker trades info', required=False)
# parser.add_argument('-testaccount', '--testaccount', help='test position file from dav location', action=argparse.BooleanOptionalAction)

args = parser.parse_args()
date = args.date
# DESTINATION = args.destination
NOSENDORDER = args.nosend
TOGGLE_EXCLUDE = args.toggleexclude
ALL_ACCOUNTS = args.allaccounts
FILTER_FLAG = args.flag
CHECK_SOME_TICKER = [c_.strip() for c_ in args.checkticker.split(',')] if args.checkticker is not None else []
# flag_testaccount = args.testaccount
# config_args = [c.strip() for c in args.config.split(',')]

FUTURE_HOLD_DIR = '期货汇总'
DEFAULT_TRADE_INTERVAL_MINS = 40

if not Calendar.is_trading_day(date):
    date = Calendar.last_trading_day(date).strftime('%Y%m%d')
pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')


# rs = conn_redis()
order_records = {}
account_records = {}
product_records = {}
check_some_ticker_res_df = pd.DataFrame()

# update latest hold warning
# flag_to_reload_hold = str(input("""

#         是否刷新持仓hold? (y/N)

#                              """))
# if flag_to_reload_hold.lower() == 'y':
#     run_sub_process('load_latest_hold.py')
logger.warning(f'\n\n         确保持仓hold文件已经更新, 日期: {date}\n')


# merge account config
accounts_fj = accounts_fj['accounts']
accounts_config = {**zs_accounts_dict, **test_accounts_dict}

pre_close = get_close_adj(pre_date)
mkt_limit = get_mkt_limit(date)
zz500_preclose = get_index_close('000905', pre_date)
zz500_multiplier = 200
st_stocks = get_st_stocks()
constitutions = get_cons_tag_series(date)
# product_target_future_count = {}

# index_close = get_index_close('000905', pre_date)
if pre_close.empty:
    logger.error('get pre close failed')


trade_order_args = {
    # test account
    
###########################################################
    # 'test' : {
    #     'fund_account' : 'D1A_KFZHZXSM01',
    #     # 'fund_account' : 'D1SHK_2412001',
    #     'order_type' : 'matic_otc',
    # },
    # '宽辅思贤专享中性1号_华鑫': {
    #     # 'order_type' : 'hxtrade_algo',
    #     'order_type' : 'hxtrade_scan',
    #     'account_id' : 'kfsxzx1',
    #     'algorithm' : 'HX_SMART_VWAP',
    #     'AlgoParam' : {},
    #     'LimAction' : 1,
    #     'basket_id' : 'sx1_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    #     'acc'       : '宽辅思贤专享中性1号_华鑫',
    # },

    '宽辅思贤专享中性1号_华鑫': {
        'order_type' : 'kafang_algo_scan',
        'sh_sz_double_mode' : True,
        'only_SH': False,
        'only_SZ': False,
        'sh_clientname' : 'XSZX1h',
        'sz_clientname' : 'XSZX1h_sz',
        'algorithm' : 'kf_vwap_plus',
        'AlgoParam' : {
            # 'maxpercentageF' : 10,
        },
        'LimitAction' : 1,
        'AftAction' : 0,
        'trading_date' : date,
        'pre_close' : pre_close,
        'acc'       : '宽辅思贤专享中性1号_华鑫',
        # 'basket_id' : 'sx1{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    # '宽辅思贤专享中性1号_华鑫': {
    #     'order_type' : 'kafang_algo',
    #     # 'clientName' : 'XSZX1h',
    #     'sh_sz_double_mode' : True,
    #     'sh_clientname' : 'XSZX1h',
    #     'sz_clientname' : 'XSZX1h_sz',
    #     'orderType' : 'VWAP_Plus',
    #     'trading_date' : date,
    #     'limitAction' : 1,
    #     'afterAction' : 0,
    #     'algoParam' : 'basket_id=sx1h{}'.format(datetime.datetime.now().strftime('%H%M')),      # 属性之间用:隔开, plus算法有UpLimitF 及 DownLimitF 参数
    #     'pre_close' : pre_close,
    #     'acc'       : '宽辅思贤专享中性1号_华鑫',
    # },

    '宽辅思贤专享中性1号_中金多空': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '*********',
        'secondary_account' : True,
        'secondary_account_clientname' : '*********_空头',
        'short_sell_flag' : 4,         # 融券卖出
        'close_short_flag' : 3,        # 买券还券
        # 'algorithm' : 'kf_vwap_plus',
        'algorithm' : 'kf_pov_core',
        'AlgoParam' : {
            'maxpercentageF' : 10,
        },
        'LimAction' : 0,
        'AftAction' : 0,
        'trading_date' : date,
        'basket_id' : 'sx1_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    '宽辅专享6号_中金场外': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '*********',
        'secondary_account' : True,
        'secondary_account_clientname' : '*********_空头',
        'short_sell_flag' : 4,         # 融券卖出
        'close_short_flag' : 3,        # 买券还券
        # 'algorithm' : 'kf_vwap_plus',
        'algorithm' : 'kf_pov_core',
        'AlgoParam' : {
            'maxpercentageF' : 10,
        },
        'LimAction' : 1,
        'AftAction' : 0,
        'trading_date' : date,
        'basket_id' : 'zx6_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },

    '宽辅思贤专享中性2号_光大场外': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : 'HFSX2H_sh',
        'secondary_account' : True,
        'secondary_account_clientname' : 'HFSX2H_rq',
        'short_sell_flag' : 2,         # 普通卖出
        'close_short_flag' : 1,        # 普通买入
        'algorithm' : 'kf_pov_core',
        'AlgoParam' : {
            'maxpercentageF' : 10,
        },
        'LimAction' : 1,
        'AftAction' : 0,
        'trading_date' : date,
        'basket_id' : 'sx2_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },

    '宽辅专享6号_光大多空': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : 'HFZX6H_sh',
        'secondary_account' : True,
        'secondary_account_clientname' : 'HFZX6H_rq',
        'short_sell_flag' : 2,         # 普通卖出
        'close_short_flag' : 1,        # 普通买入
        'algorithm' : 'kf_pov_core',
        'AlgoParam' : {
            'maxpercentageF' : 10,
        },
        'LimAction' : 1,
        'AftAction' : 0,
        'trading_date' : date,
        'basket_id' : 'zx6_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },

    '宽辅臻好专享_华泰' : {
        # 'fund_account' : 'D1A_KFZHZXSM01',
        'order_type' : 'matic_otc_basket',
        'strategy_type' : 'VWAP',
        'strategy_style' : '平稳',
        'LimAction' : True,
        'mkt_limit': mkt_limit,
        'pre_close': pre_close,
    },
    
    '宽辅泓涛专享2号_华泰场外' : {
        'order_type' : 'matic_otc_basket',
        'strategy_type' : 'VWAP',
        'strategy_style' : '平稳',
        'LimAction' : True,
        'mkt_limit': mkt_limit,
        'pre_close': pre_close,
    },
    
    '远航安心中性1号' : {
        'account_id' : '**************',
        'order_type' : 'gtrade',
        'strategy_type' : 'HX_SMART_VWAP',
        'strategy_paras' : 'PR=0.05',
        # 'strategy_paras' : 'PR=0.10;MOA=2500',
        'no_trade_extreme' : '1',
        'order_filename' : '{account}.{num}.{date}.交易单{suffix}.csv',

        'trans_posiion' : False,
        'old_account_id' : '**************',
        'new_account_id' : '**************',
        'old_account_name' : '远航安心中性1号_国信临1',
        'new_account_name' : '远航安心中性1号_国信临2',
        'pre_close' : pre_close,
    },
    # '远航安心中性1号_银河' : {
    #     'order_type' : 'yinhedma_kafang',
    #     'strategy'   : 'VWAP_CORE',
    #     'clientName' : 'OTC000125545',
    #     'orderType'  : 'kf_vwap_core',
    #     'trading_date' : date,
    #     'limitAction'  : 0,
    #     'afterAction'  : 0,
    #     'algoParam'    : 'basket_id=11_{}'.format(datetime.datetime.now().strftime('%H%M')),      # 属性之间用:隔开
    # },
    # '远航安心中性1号_招商' : {
    #     'order_type' : 'zhaoshang_kafang',
    #     'clientName' : 'S8000015669',
    #     'orderType' : 'VWAP_Plus',
    #     'trading_date' : date,
    #     'limitAction' : 0,
    #     'afterAction' : 0,
    #     'algoParam' : 'basket_id=zs{}'.format(datetime.datetime.now().strftime('%H%M')),      # 属性之间用:隔开, plus算法有UpLimitF 及 DownLimitF 参数
    # },
    
    '宽辅泓涛专享1号' : {
        'account_id' : '**************',
        'order_type' : 'gtrade',
        'strategy_type' : 'HX_SMART_VWAP',          # 算法
        'strategy_paras' : 'PR=0.05',
        'no_trade_extreme' : '1',
        'order_filename' : '{account}.{num}.{date}.交易单{suffix}.csv',

        'trans_posiion' : False,
        'old_account_id' : '**************',
        'new_account_id' : '**************',
        'old_account_name' : '宽辅泓涛专享1号_国信临1',
        'new_account_name' : '宽辅泓涛专享1号_国信临2',
        'pre_close' : pre_close,
    },

    # '宽辅泓涛专享1号_东方': {
    #     'order_type' : 'dfdma_kafang',
    #     'prdt_name' : '宽辅泓涛专享1号',   
    #     'unit_name' : '系统自动新增单元',  
    #     'acc_name' :  '宽辅泓涛专享1号', 
    #     'strategy_instance': 'kf_vwap_plus',
    #     'trading_date' : date,
    #     'updown_limit_rule' : '涨跌停终止交易',
    #     'order_remark' : '涨幅限制=0%;跌幅限制=0%;篮子编号=ht_{}'.format(datetime.datetime.now().strftime('%H%M')),    # 属性之间用:隔开
    # },
    '宽辅泓涛专享1号_东方': {
        'order_type' : 'kafang_ato_scan',
        # 'ClientName' : '1986',
        # 'UnitId' : '************',
        # 'ProductId' : '********',
        'ClientName' : '2041',
        'UnitId' : '************',
        'ProductId' : '********',
        'algorithm' : 'kf_vwap_plus',
        'trading_date' : date,
        'basket_id' : 'ht{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    '宽辅泓涛专享1号_广发': {
        'order_type' : 'guangfa_file',
        'strategy_type' : 'vwap_plus',
        'product_id' : 'DEV1100105519',
        'port_id' : '1194',
        'trading_date' : date,
        'updown_limit_rule' : 0,
        'no_trade_expire' : '否',
        'mkt_limit': mkt_limit,
        'pre_close': pre_close,
    },

    '宽辅专享1号': {
        'account_id' : '**************',
        'order_type' : 'gtrade',
        'strategy_type' : 'HX_SMART_VWAP',          # 算法
        'strategy_paras' : 'PR=0.05',
        'no_trade_extreme' : '1',
        'order_filename' : '{account}.{num}.{date}.交易单{suffix}.csv',

        'trans_posiion' : False,
        'old_account_id' : '**************',
        'new_account_id' : '**************',
        'old_account_name' : '宽辅专享1号_国信临1',
        'new_account_name' : '宽辅专享1号_国信临2',
        'pre_close' : pre_close,
    },
    
    # '宽辅专享1号_浙商': {
    #     'order_type' : 'kafang_algo',
    #     'clientName' : 'KUANFU-ZHUANXIANG1',
    #     'orderType' : 'VWAP_Plus',
    #     'trading_date' : date,
    #     'limitAction' : 0,
    #     'afterAction' : 0,
    #     'algoParam' : 'basket_id=zx1h{}'.format(datetime.datetime.now().strftime('%H%M')),      # 属性之间用:隔开, plus算法有UpLimitF 及 DownLimitF 参数
    # },

    # '宽辅专享1号_浙商': {
    # 'test': {
    #     'order_type' : 'kafang_atx',
    #     'account_id' : '********',
    #     'strategy_type' : 'VWAP',
    #     'strategy_instance' : 'kf_vwap_core',
    #     'trading_date' : date,
    #     'limit_action' : '0',    
    #     'order_remark' : '篮子编号=test_{}'.format(datetime.datetime.now().strftime('%H%M')),
    # },

    # 'test': {
    #     'order_type' : 'kafang_atx_scan',
    #     'ClientName' : '179_E_rq',
    #     'algorithm' : 'kf_vwap_core',
    #     'trading_date' : date,
    #     'basket_id' : 'test{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    # },
    # 'test': {
    #     'order_type' : 'cats_generic',
    #     # AlgoType=HX_SMARTVWAP;beginTime=90000;endTime=163500;limitUpDownTrading=false;maxRate=0.2;limitPrice=0.0;extraParam=
    #     'tradingaccount_type'  : 'S0',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
    #     'tradingaccount'       : '********',
    #     'algorithm'            : 'HX_SMARTVWAP',
    #     'algorithm_params'     :  {
    #         # 'beginTime'         : '', 
    #         # 'endTime'           : '',
    #         'limitUpDownTrading' : False,
    #         'maxRate'            : 0.2,
    #         'limitPrice'         : 0,
    #         'extraParam'         : '',
    #     },
    # },
    # '远航安心中性6号_中信': {
    #     'order_type' : 'zhongxin_cats',
    #     'tradingaccount_type'  : 'SZQFEQD',
    #     'tradingaccount'       : '118965--CNY--RM',
    #     'algorithm'            : 'SmartVWAP3',
    #     'algorithm_params'     :  {
    #         # 'beginTime'         : '', 
    #         # 'endTime'           : '',
    #         'limitPrice'         : 0,
    #         'participateRate'    : 0.0,
    #         'tradingStyle'       : 1,
    #     },
    # },

    '远航安心中性6号_中信多空': {
        # 'order_type' : 'zhongxin_cats',
        'order_type' : 'cats_generic',
        'tradingaccount_type'  : 'SZQFEQD',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
        'tradingaccount'       : '118965--CNY--RM--B',
        # 'algorithm'            : 'SmartVWAP3',
        'algorithm'            : 'SmartVolumeInline3',
        'algorithm_params'     :  {
            # 'beginTime'         : '', 
            # 'endTime'           : '',
            'limitPrice'         : 0,
            'participateRate'    : 0.3,
            'tradingStyle'       : 1,
            'openCall'           : 'true',    # 参与集合竞价, SmartVolumeInline3 算法
        },
    },

    '宽辅泓涛专享1号_中信多空': {
        # 'order_type' : 'zhongxin_cats',
        'order_type' : 'cats_generic',
        'tradingaccount_type'  : 'SZQFEQD',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
        'tradingaccount'       : '118966--CNY--RM',
        # 'algorithm'            : 'SmartVWAP3',
        'algorithm'            : 'SmartVolumeInline3',
        'algorithm_params'     :  {
            # 'beginTime'         : '', 
            # 'endTime'           : '',
            'limitPrice'         : 0,
            'participateRate'    : 0.3,
            'tradingStyle'       : 1,
            'openCall'           : 'true',    # 参与集合竞价, SmartVolumeInline3 算法
        },
    },
    
    '宽辅专享6号_中信多空': {
        # 'order_type' : 'zhongxin_cats',
        'order_type' : 'cats_generic',
        'tradingaccount_type'  : 'SZQFEQD',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
        'tradingaccount'       : '121531--CNY--RM',
        # 'algorithm'            : 'SmartVWAP3',
        'algorithm'            : 'SmartVolumeInline3',
        'algorithm_params'     :  {
            # 'beginTime'         : '', 
            # 'endTime'           : '',
            'limitPrice'         : 0,
            'participateRate'    : 0.3,
            'tradingStyle'       : 1,
            'openCall'           : 'true',    # 参与集合竞价, SmartVolumeInline3 算法
        },
    },

    # beginDate=********;
    # endDate=********;
    # beginTime=90000;
    # endTime=163500;
    # duration=;
    # maxRate=0.2;
    # aggressiveLevel=2;
    # limitPrice=0.0;
    # dynamicLimitBenchmarkType=2;
    # dynamicLimitPriceOffSetPct=0.0;
    # minAmount=0.0;
    # isDownOrUpStopStockNotTrade=false;
    # isDownStopNotSellOrUpStopNotBuy=false;
    # maxCancelCnt=0;
    # maxOpenOrderQty=0;
    # minCloseOrderQty=0;
    # iWouldTriggerBenchmark=2;
    # iWouldTriggerPrice=0.0;
    # iWouldTriggerOffSetPct=0.0;
    # iWouldPctOfTarget=0.0;
    # iWouldPctOfRemain=0.0;
    # iWouldMaxRate=0.0;
    # triggerType=N;
    # triggerPrice=0.0;
    # expectTradingDuration=0;
    # expectEndTime=;
    # allowExceedTargetQty=0;
    # cancelAllOrdersWhenModify=false;
    # ignoreAlgoTradingOrderCountLimit=false

    # '宽辅泓涛专享2号_中信': {
    #     # 'order_type' : 'zhongxin_cats',
    #     'order_type' : 'cats_generic',
    #     'tradingaccount_type'  : 'SHHTS0',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
    #     'tradingaccount'       : '4104866',
    #     # 'algorithm'            : 'SmartVWAP3',
    #     'algorithm'            : 'ZJVWAP6',
    #     'algorithm_params'     :  {
    #         'beginDate':date,
    #         'endDate':date,
    #         # 'beginTime=90000
    #         # 'endTime=163500
    #         'duration':'',
    #         'maxRate':0.2,
    #         'aggressiveLevel':2,
    #         'limitPrice':0.0,
    #         'dynamicLimitBenchmarkType':2,
    #         'dynamicLimitPriceOffSetPct':0.0,
    #         'minAmount':0.0,
    #         'isDownOrUpStopStockNotTrade':False,
    #         'isDownStopNotSellOrUpStopNotBuy':False,
    #         'maxCancelCnt':0,
    #         'maxOpenOrderQty':0,
    #         'minCloseOrderQty':0,
    #         'iWouldTriggerBenchmark':2,
    #         'iWouldTriggerPrice':0.0,
    #         'iWouldTriggerOffSetPct':0.0,
    #         'iWouldPctOfTarget':0.0,
    #         'iWouldPctOfRemain':0.0,
    #         'iWouldMaxRate':0.0,
    #         'triggerType':'N',
    #         'triggerPrice':0.0,
    #         'expectTradingDuration':0,
    #         'expectEndTime':'',
    #         'allowExceedTargetQty':0,
    #         'cancelAllOrdersWhenModify':False,
    #         'ignoreAlgoTradingOrderCountLimit':False,
    #     },
    # },

    '宽辅泓涛专享2号_中信': {
        # 'order_type' : 'zhongxin_cats',
        'order_type' : 'cats_generic',
        'tradingaccount_type'  : 'SHHTS0',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
        'tradingaccount'       : '4104866',
        'algorithm'            : 'FT_AIWAP',
        'algorithm_params'     :  {
            'limitPriceContinue': True,
        },
    },

    '宽辅泓涛专享1号_中信': {
        # 'order_type' : 'zhongxin_cats',
        'order_type' : 'cats_generic',
        'tradingaccount_type'  : 'SHHTS0',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
        'tradingaccount'       : '4102976',
        'algorithm'            : 'FT_AIWAP',
        'algorithm_params'     :  {
            'limitPriceContinue': True,
        },
    },
    # '宽辅泓涛专享1号_中信': {
    #     # 'order_type' : 'zhongxin_cats',
    #     'order_type' : 'cats_generic',
    #     'tradingaccount_type'  : 'SHHTS0',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户
    #     'tradingaccount'       : '4102976',
    #     # 'algorithm'            : 'SmartVWAP3',
    #     'algorithm'            : 'ZJVWAP6',
    #     'algorithm_params'     :  {
    #         'beginDate':date,
    #         'endDate':date,
    #         # 'beginTime=90000
    #         # 'endTime=163500
    #         'duration':'',
    #         'maxRate':0.2,
    #         'aggressiveLevel':2,
    #         'limitPrice':0.0,
    #         'dynamicLimitBenchmarkType':2,
    #         'dynamicLimitPriceOffSetPct':0.0,
    #         'minAmount':0.0,
    #         'isDownOrUpStopStockNotTrade':False,
    #         'isDownStopNotSellOrUpStopNotBuy':False,
    #         'maxCancelCnt':0,
    #         'maxOpenOrderQty':0,
    #         'minCloseOrderQty':0,
    #         'iWouldTriggerBenchmark':2,
    #         'iWouldTriggerPrice':0.0,
    #         'iWouldTriggerOffSetPct':0.0,
    #         'iWouldPctOfTarget':0.0,
    #         'iWouldPctOfRemain':0.0,
    #         'iWouldMaxRate':0.0,
    #         'triggerType':'N',
    #         'triggerPrice':0.0,
    #         'expectTradingDuration':0,
    #         'expectEndTime':'',
    #         'allowExceedTargetQty':0,
    #         'cancelAllOrdersWhenModify':False,
    #         'ignoreAlgoTradingOrderCountLimit':False,
    #     },
    # },

    # '宽辅泓涛专享1号_中信两融': {
    #     # 'order_type' : 'zhongxin_cats',
    #     'order_type' : 'cats_generic',
    #     'tradingaccount_type'  : 'SHHTSC',   # SZQFEQD为场外互换, 'C'为场内信用账户, '0'为股票普通户 , SHHTSC HTS快速（上海信用）
    #     'tradingaccount'       : '4102976',
    #     'algorithm'            : 'ZJVOL6',
    #     'algorithm_params'     :  {
    #         # "AlgoType": "ZJVOL6",
    #         "maxRate": 0.2,
    #         "beginDate": date,
    #         "endDate": date,
    #         "duration": "",  # 表示空  duration=
    #         "aggressiveLevel": 2,
    #         "limitPrice": 0.0,
    #         "dynamicLimitBenchmarkType": 2,
    #         "dynamicLimitPriceOffSetPct": 0.0,
    #         "minAmount": 2000,
    #         "isDownOrUpStopStockNotTrade": 'false',
    #         "isDownStopNotSellOrUpStopNotBuy": 'false',
    #         "maxCancelCnt": 0,
    #         "maxOpenOrderQty": 0,
    #         "minCloseOrderQty": 0,
    #         "iWouldTriggerBenchmark": 2,
    #         "iWouldTriggerPrice": 0.0,
    #         "iWouldTriggerOffSetPct": 0.0,
    #         "iWouldPctOfTarget": 0.0,
    #         "iWouldPctOfRemain": 0.0,
    #         "iWouldMaxRate": 0.0,
    #         "cancelAllOrdersWhenModify": 'false'
    #     },
    # },
    
    # '远航安心中性6号_银河': {
    #     'order_type' : 'yinhedma_kafang',
    #     'clientName' : 'OTC000127384',
    #     'strategy' : 'VWAP_PLUS',
    #     'orderType' : 'kf_vwap_plus',
    #     'trading_date' : date,
    #     'limitAction' : 0,
    #     'afterAction' : 0,
    #     'algoParam' : 'basket_id=6hao_{}'.format(datetime.datetime.now().strftime('%H%M')),      # 属性之间用:隔开
    # },

    # '远航安心中性6号_国君': {
    #     'order_type' : 'kafang_atx',
    #     'account_id' : '宽辅远航安心市场中性6号私募（DMA）',
    #     'strategy_type' : 'VWAP',
    #     # 'strategy_instance' : 'HX_SMART_VWAP',
    #     'strategy_instance' : 'JINGLE_VWAP',
    #     'trading_date' : date,
    #     'limit_action' : '2',    # JINGLE_VWAP 算法参数 
    #     'order_remark' : '篮子编号=6hao_{}'.format(datetime.datetime.now().strftime('%H%M')),
    # },
    '远航安心中性6号_国君': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '宽辅远航安心市场中性6号私募（DMA）',
        'algorithm' : 'JINGLE_VWAP',
        'LimAction' : 2,
        'trading_date' : date,
        'basket_id' : '6hao_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },

    # '远航安心中性2号_浙商': {
    #     'order_type' : 'kafang_atx_scan',
    #     'ClientName' : '宽辅远航安心市场中性2号',
    #     'algorithm' : 'kf_vwap_core',
    #     'LimitAction' : 0,
    #     'trading_date' : date,
    #     'basket_id' : '6hao_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    # },

    # '远航安心中性6号_国联': {
    #     'order_type' : 'dfdma_kafang',
    #     'prdt_name' : '宽辅远航安心市场中性6号',
    #     'unit_name' : '系统自动新增单元',  
    #     'acc_name' :  '666600028', 
    #     'strategy_instance': 'kf_vwap_plus',
    #     'trading_date' : date,
    #     'updown_limit_rule' : '涨跌停终止交易',
    #     'order_remark' : '涨幅限制=0%;跌幅限制=0%;篮子编号=6hao_{}'.format(datetime.datetime.now().strftime('%H%M')),    # 属性之间用:隔开
    # },
    
    # '远航安心中性6号_国联': {
    #     'order_type' : 'kafang_ato_scan',
    #     'ClientName' : '666600028',
    #     'UnitId' : '100000220001',
    #     'ProductId' : '10000022',
    #     'algorithm' : 'kf_vwap_plus',
    #     'trading_date' : date,
    #     'basket_id' : '6hao_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    # },
    
    '宽辅500指增1号_天风': {
        'order_type' : 'yuliang_scan',
        'ClientName' : '67003958',
        'ClientId' : '67003958',
        'FundId' : '67003958',
        # 'algorithm' : 'YLKF-smartvwap',
        'algorithm' : 'HX_SMART_VWAP',
        'AlgoParam' : {
            # 'min_amt' : 2000,
            # 'min_qty' : 0,
            # 'version' : 0,
            # 'must_complete' : 'true',
            # 'open_auction' : 'true',
        },
        'LimitAction' : 0,
        'AftAction' : 0,
        'trading_date' : date,
        'basket_id' : '500zz_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    '宽辅思贤专享中性2号_东财' : {
        'order_type' : 'dfemc_sweep',
        'fundID'   : '************',
        'algoType' : 'KFVWAPPLUS',
        'algoParam' : {
            'limit_action' : 0,
            'after_action' : 0,
        },
        'algoTag' : 'KF_ATGO',
        'order_filename' : 'BUSALGOORDER_{num}_{account}.{nowtime}.csv',
    },
    '宽辅臻好专享_国君': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '宽辅臻好专享证券',
        # 'algorithm' : '(gtja)_HX_SMART_VWAP',
        # 'LimAction' : 0,
        # 'AlgoParam' : {
        # },
        
        'algorithm' : '(gtja)_ld_vwap',
        # 'algorithm' : 'HX_SMART_VWAP',
        'LimAction' : 0,
        'AlgoParam' : {
            'participation_rate' : 10,
        },
        'trading_date' : date,
        'basket_id' : 'zhzx_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    '宽辅1000指增1号_国君': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '宽辅中证1000指数增强1号证券',
        # 'algorithm' : '(gtja)_HX_SMART_VWAP',
        # 'LimAction' : 0,
        # 'AlgoParam' : {
        # },
        
        'algorithm' : '(gtja)_ld_vwap',
        # 'algorithm' : 'HX_SMART_VWAP',
        'LimAction' : 0,
        'AlgoParam' : {
            'participation_rate' : 10,
        },
        'trading_date' : date,
        'basket_id' : '1000zz_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    '宽辅金品中国专享': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '********',
        'algorithm' : 'ft_vwap_ai_plus',
        'LimAction' : 0,
        'AlgoParam' : {
        },
        'trading_date' : date,
        'basket_id' : 'jpzg_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    # '宽辅臻好专享_国君': {
    #     'order_type' : 'kafang_atx',
    #     'account_id' : '宽辅臻好专享证券',
    #     'strategy_type' : 'VWAP',
    #     'strategy_instance' : '(gtja)_ld_vwap',
    #     'trading_date' : date,
    #     'limit_action' : 0,    
    #     'strategy_paras' : {
    #         '市场参与率' : 10,
    #         '篮子编号' : 'zhzx_{}'.format(datetime.datetime.now().strftime('%H%M')),
    #     },
    # },
    '远航安心中性5号_华金': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '********',
        'algorithm' : 'kf_vwap_plus',
        'LimAction' : 0,
        'AlgoParam' : {
        },
        'trading_date' : date,
        'basket_id' : 'zhongx5h_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    '宽辅泓涛专享2号_山西': {
        'order_type' : 'kafang_atx_scan',
        'ClientName' : '********',
        'algorithm' : 'kf_vwap_plus',
        'LimAction' : 0,
        'AlgoParam' : {
        },
        'trading_date' : date,
        'basket_id' : 'hongtao2h_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
    },
    
    '远航安心中性1号_广发': {
        'order_type' : 'touyitong_scan',
        'projectid' : '5314',
        'basket_id' : 'zhongxin1hao_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
        'algo_params' : {
            # algo_type=1001|start_time=103000|end_time=150000|algo_price=0|limit_action=0|part_rate=0.3|min_amt=10000
            'algo_type' : 5001,    # 3001（VWAP-PLUS），3002（TWAP-PLUS） 4001（FT-WAP-AI）、4003（FT-WAP-AI-PLUS） 5001（HX-SMART-VWAP）、5002（HX-SMART-TWAP）、15001（HX-SMART-T0）
            'algo_price' : 0,
            'limit_action' : 0,
            # 'part_rate' : 0.3,
        },
        'order_filename' : 'instr_zhognxin1hao{suffix}.{date_time}.csv',
    },
    '宽辅思贤专享中性2号_广发': {
        'order_type' : 'touyitong_scan',
        'projectid' : '3348',
        'basket_id' : 'sixian2_{}'.format(datetime.datetime.now().strftime('%H%M%S')),    # 属性之间用:隔开
        'algo_params' : {
            # algo_type=1001|start_time=103000|end_time=150000|algo_price=0|limit_action=0|part_rate=0.3|min_amt=10000
            'algo_type' : 4003,    # 3001（VWAP-PLUS），3002（TWAP-PLUS） 4001（FT-WAP-AI）、4003（FT-WAP-AI-PLUS） 5001（HX-SMART-VWAP）、5002（HX-SMART-TWAP）、15001（HX-SMART-T0）
            'reach_limit_continue' : 0,
            'after_action' : 0,
        },
        'order_filename' : 'instr_sixian2{suffix}.{date_time}.csv',
    },

    '模拟股票2' : {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9003', # 智能算法
        'brokertype' : '2',    # 股票
        'account_id' : '2000586',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 1,
            'lbbl' : 0.2,
            'wtzxje' : 2000,
        },
    },
    '模拟股票3' : {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9003',
        'brokertype' : '2',
        'account_id' : '2031455',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 1,
            'lbbl' : 0.2,
            'wtzxje' : 2000,
        },
    },
    
    '宽辅专享7号_江海' : {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9004',
        'brokertype' : '2',
        'account_id' : '********',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 'VWAPA1',
        },
    },
    '宽辅泓涛专享2号_国融' : {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9004',
        'brokertype' : '2',
        'account_id' : '************',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 'VWAPA1',
        },
    },
    '宽辅1000指增1号_平安': {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9003',
        'brokertype' : '2',
        'account_id' : '************',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 1,
            'lbbl' : 0.2,
            'wtzxje' : 2000,
        },
    },
    '宽辅专享6号_光大': {
        'order_type' : 'xuntou_dbf',
        'price_type' : '9003',
        'brokertype' : '2',
        'account_id' : '********',
        'AlgoParam' : {
            'ddlx' : 0,
            'sfmc' : 1,
            'lbbl' : 0.2,
            'wtzxje' : 2000,
        },
    },

}

trade_config = get_trade_config(trade_config_file)
accounts_config = update_sub_config_in_main_config(accounts_config, 'trade_config', trade_config)
accounts_config = update_sub_config_in_main_config(accounts_config, 'trade_order_args', trade_order_args)
# trade_db_conn = get_trade_db_conn()

product_future_hold_df = get_product_future_hold_to_info(pre_date)  # 期货昨日持仓
# product_future_target_unit = get_product_target_futures_unit(date)  # 目标仓位weight / 昨日指数点位
# print(product_future_target_unit)

position_zero_lst_all_accounts = [ ]
position_zero_by_account = {
    '宽辅泓涛专享1号_东方' : [601113],
    '远航安心中性6号_国君' : [601211, 688479, 2437, 600837],
    '宽辅金品中国专享' : [689009],
}

non_buy_stock_lst_all_accounts = [

]

non_buy_stock_by_account = {
    
}

non_sell_stock_lst_all_accounts = [

]

non_sell_stock_by_account = {
    
}

# for account_name in ['远航安心中性1号']:  # kf + fj
# for account_name in accounts_config.keys():  # kf + fj
for account_name in ['超量子中泰']:  # kf + fj
    # print(account_name)
    # get trade config
    account_config = get_account_config(account_name, accounts_config, 'trade_config')
    trade_order_args = get_account_config(account_name, accounts_config, 'trade_order_args')
    product_name = get_account_config(account_name, accounts_config, 'product_name')
    
    # 特殊账户参数调整
    trade_order_args = adjust_trade_order_args(account_name, trade_order_args, account_config)
    account_config = adjust_account_config(account_name, account_config)
    # print('config:\n', json.dumps(account_config, indent=4, ensure_ascii=False))
    # print('trade_order_args:\n', json.dumps(trade_order_args, indent=4, ensure_ascii=False))
    
    # account filter
    if account_config.get('active') != True:
        continue
    if get_account_exclude_flag(account_config) == False:
        continue
    if filter_account_flag(account_config) == False:
        continue
    
    logger.info(f'\n')
    logger.info(f'生成order ... {account_name} ...... type: {trade_order_args.get("order_type")}')
    
    # 从hold 这里开始才有 account_records 里的account_name条目
    # get hold
    
    # TODO
    hold = get_latest_hold(account_name, date, account_config)

    # tmp_hold_file = find_latest_remote_file_on_date(sftp_clent_ninner, os.path.join(account_name, 'hold'), file_prefix='hold_', date=date, timeby='083000')
    # hold = read_remote_file(os.path.join(account_name, 'hold', tmp_hold_file), src_type='ninner')
    
    
    
    
    hold = hold.rename(columns={'ticker': 'symbol', 'volume': 'hold_shares', 'available_volume': 'available_shares',
                            '代码' : 'symbol', '持仓量' : 'hold_shares',
                            '证券代码' : 'symbol', '持有数量' : 'hold_shares', '当前数量' : 'hold_shares'})
    
    # morning_hold = get_stock_morning_hold(account_name, date)
    # morning_hold = morning_hold.rename(columns={'ticker': 'symbol', 'volume': 'hold_shares', 'available_volume': 'available_shares',})
    # morning_hold['symbol'] = morning_hold['symbol'].astype(int)
    # update morning_hold to trade_order_args
    
    # print(hold.head())
    # get target
    target = custom_get_target_by_date(account_name, date, account_config).rename(columns={'ticker':'symbol', 'volume':'target_shares'})
    
    # target = get_target_by_date(account_name, date).rename(columns={'ticker':'symbol', 'volume':'target_shares'})
    
    
    # check clean hold, target
    hold = check_clean_hold(account_name, hold, product_name)
    target = check_clean_position(account_name, target, product_name)
    
    # cal changes
    changes = cal_changes(account_name, hold, target, account_config)

    # check stock weight 集中度
    if account_name in ['宽辅泓涛专享1号_广发', '远航安心中性6号_国君', '远航安心中性6号_银河']:
        check_stock_weight(target, pre_close)

    # check some ticker
    check_some_ticker_res_df = check_trades_some_ticker(account_name, changes, CHECK_SOME_TICKER, check_some_ticker_res_df)
    
    # update available fund
    available_fund = get_available_fund(account_name, date, account_config)
    update_records_info(account_records, account_name, 'available_fund', available_fund)
    update_order_time(account_name, trade_order_args)
    
    # changes to order
    order_type = trade_order_args.pop('order_type', 'xt1')
    # ====== 中信客户端特殊处理, 交易参数trade_config 添加hold, position
    if 'cats' in order_type:  # cats 客户端交易单
        trade_order_args['hold'] = hold
        trade_order_args['target'] = target
        trade_order_args['pre_close'] = pre_close
        trade_order_args['acc'] = account_name
    if 'cats' in order_type and '多空' in account_name:
        recall = get_recall_num(account_name, date)
        trade_order_args['recall'] = recall
    # ====== gtrade 客户端特殊处理, 交易参数trade_config 添加hold, position
    if 'gtrade' in order_type:
        # trade_order_args['hold'] = hold
        # trade_order_args['target'] = target
        trade_order_args['pre_close'] = pre_close
        trade_order_args['acc'] = account_name
    
    # ======
    order_file_suffix = account_config.pop('order_file_suffix')
    update_records_info(account_records, account_name, 'order_file_suffix', order_file_suffix)
    
    # get product future num in trade db
    # hold_future_num = get_account_future_num(account_name, trade_db_conn)
    # update_records_info(account_records, account_name, 'hold_future_num', hold_future_num)
    # if account_name in zs_accounts_dict.keys():
    #     hold_future_num = get_product_future_num(product_name, date=pre_date)

    # elif account_name in accounts_fj.keys():
    #     hold_future_num = get_product_short_value_from_pnl_fj(product_name, date=pre_date) / zz500_preclose / zz500_multiplier
    # else:
    #     hold_future_num = 0    
    update_records_info(account_records, account_name, 'product_name', product_name)
    # update_records_info(account_records, account_name, 'hold_future_num', hold_future_num)
    update_records_info(account_records, account_name, '交易员', account_config.get('交易员'))
    
    

    # print(order_file_suffix)
    local_order_files = generate_order_files_list(
                        changes=changes,
                        order_type=order_type,
                        local_account_dir=local_account_orders_dir,
                        account_name=account_name,
                        date=date,
                        suffix=order_file_suffix,
                        **trade_order_args
                        )
    order_records[account_name] = local_order_files

# display orders info
deal_display_orders_info(account_records)
if len(check_some_ticker_res_df) > 0:
    logger.warning(f'特定股票交易情况 : \n{check_some_ticker_res_df}')


#
# 确认无误后, 发送 order
if NOSENDORDER == True:
    logger.info('未设置发送交易单文件, 退出')
    os._exit(0)






# send_t0_volume_acc = '宽辅思贤专享中性1号_华鑫'
# if send_t0_volume_acc in order_records.keys():

#     flag_upload_t0_volume = input(f'\n        将发送 {send_t0_volume_acc} 底仓文件, 是否确认? (confirm/....)\n                             ')
#     if flag_upload_t0_volume != 'confirm':
#         logger.warning(f'{send_t0_volume_acc} 账户未发送 t0_volume 文件.')
#     else:
#         t0_volume = calc_t0_volume(
#             hold=hold.rename(
#                 columns={
#                     "symbol": "ticker",
#                     "hold_shares": "volume",
#                     "available_shares": "available_volume",
#                 }
#             ),
#             target=target.rename(columns={"symbol": "ticker", "target_shares": "volume"}),
#         )
#         t0_volume.rename(columns={"t0_volume": "volume"}, inplace=True)
#         print(t0_volume.head(2))
#         print(t0_volume.shape)
#         write_file(t0_volume, file_type='xls', dest_type='gtapi', 
#                dest_path=os.path.join(send_t0_volume_acc, 'hold', f'hold_{date}.xls'),
#                index=False)
#         logger.info(f'{send_t0_volume_acc} 底仓volume hold文件已发送.')






flag_to_transfer = str(input("""
                             
                确认交易单信息, 是否上传交易单文件? (y/n)
        
                             """))
if flag_to_transfer.lower() == 'y':
    send_order(order_records=order_records, accounts_config=accounts_config)
    
    # count how many orders sending scan orders
    # 如果有扫单记录, 则发送扫单
    # if sum([accounts_config[account_name]['trade_config'].get('sweeporder', False) for account_name in order_records]) > 0:


    # 发扫单
    scan_order_accs = [account_name for account_name in order_records.keys() if accounts_config[account_name]['trade_config'].get('sweeporder', False)==True]
    if len(scan_order_accs) > 0:
        logger.warning(f"""
                    
                    ========== 以下发送扫单 ============
                    
                    """)

        
        # 再次确认订单信息
        # print(scan_order_accs)
        deal_display_orders_info(account_records=account_records, accs=scan_order_accs)
        
        
        if  sum(['_光大场外' in acc for acc in scan_order_accs]) > 0:
            logger.warning(f"""
                    
                    
                    
                    光大场外账户 同步持仓""")
        
        flag_send_sw_order = input('\n        将发扫单, 是否确认? (confirm/....)\n\n                             ')
        if flag_send_sw_order != 'confirm':
            logger.warning(f'{scan_order_accs} 账户未发送扫单.')
        else:            
            send_sweeporder_files(order_records=order_records, accounts_config=accounts_config)

# if no, exit
else:
    logger.info('未上传委托单文件, 退出')
    os._exit(0)


# send_position_acc = '宽辅思贤专享中性1号_华鑫'
# if send_position_acc in order_records.keys():

#     flag_upload_position = input(f'\n        将发送 {send_position_acc} 持仓文件, 是否确认? (confirm/....)\n                             ')
#     if flag_upload_position != 'confirm':
#         logger.warning(f'{send_position_acc} 账户未发送 target 文件.')
#     else:
#         upload_position_gt_ftp(account_name=send_position_acc, date=date)
#         logger.info(f'{send_position_acc} 目标仓位文件position已发送.')


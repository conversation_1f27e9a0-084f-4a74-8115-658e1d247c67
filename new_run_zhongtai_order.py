import datetime
from duckdb import order
import pandas as pd
import numpy as np
import shutil
import os

from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_chaolz
from misc.tools import get_stock_adj_close
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import read_remote_file
from misc.standardize_dev.zhongtai_smartx import std_hold

from misc.trade import cal_changes

def get_target_parentorders(work_date=None, suffix=''):
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
        # targetPosition_  109156033251_Position{}_Target
    # targetFile = "/home/<USER>/targetPosition_{}.csv".format(work_date,work_date)  
    # targetFile = "targetPosition_{}.csv".format(work_date,work_date)
    targetFile = "/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_before/{}/targetPosition_{}{}.csv".format(work_date,work_date,suffix)
    try:
        df = pd.read_csv(targetFile)
        # print(df.head(3))
        return df
    except FileNotFoundError:
        print(f"文件 {targetFile} 未找到。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def GetExchange(sym):
    if sym[:1] == "6":
        return sym+".SH"
    return sym+".SZ"

def stk_lot_size(sym):
    if sym[:3]=="688":
        return 200
    return 100     


def get_target(work_date, suffix=''):
    targets = get_target_parentorders(work_date, suffix)
    targets_df = targets.copy()
    targets_df['symbol']=targets_df['symbol'].astype(int)
    targets_df.rename(columns={'target': 'target_shares', }, inplace=True)
    targets_df['direction'] = targets_df['target_shares'].apply(lambda x: 1 if x > 0 else -1)
    targets_df['target_shares'] = targets_df['target_shares'].abs()
    
    return targets_df[['symbol', 'target_shares', 'direction']]


def get_hold(work_date):
    hold_file = f'数据导出/中泰SmartX/自动导出/{work_date}/xtp_109156033251_Position.csv'
    df = read_remote_file(hold_file, src_type='wintrader')
    df = std_hold(df)
    df = df[['ticker', 'volume', 'available_volume']].rename(columns={'ticker': 'symbol', 'volume': 'hold_shares', 'available_volume': 'available_shares'})
    df['direction'] = df['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    df['hold_shares'] = df['hold_shares'].abs()
    return df


def get_hold_from_targetorder(work_date, suffix=''):
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold = hold[['symbol', 'position']]
    hold.rename(columns={'position': 'hold_shares'}, inplace=True)
    hold['available_shares'] = hold['hold_shares']
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold


def get_target_as_hold(work_date, suffix=''):
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold = hold[['symbol', 'target']]
    hold.rename(columns={'target': 'hold_shares'}, inplace=True)
    hold['available_shares'] = hold['hold_shares']
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold



def vwap_target(work_date, suffix=''):
    targets_df = get_target(work_date, suffix)

    
    
    hold_df = get_hold(work_date)
    # hold_df = get_hold_from_targetorder(work_date, '')
    # hold_df = get_target_as_hold(work_date, '')
    
    
    
    
    pre_date = Calendar.last_trading_day(work_date).strftime('%Y%m%d')
    pre_close = get_stock_adj_close(pre_date).rename(columns={'ticker': 'symbol',})
    changes, info = cal_changes(hold_df, targets_df, pre_close)
    
    print(f'info: {info}')
    orders = pd.DataFrame(columns=['type', 'symbol', 'side', 'qty'])
    orders['symbol'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    orders['symbol'] = orders['symbol'].apply(GetExchange)
    orders['type']= 1
    orders['side']= changes['order_shares'].apply(lambda x: 'b' if x>0 else 's')
    orders['qty'] = changes['order_shares'].abs()
    orders = pd.concat([orders, pd.DataFrame({'type': [0], 'symbol': [pd.NA], 'side': [pd.NA], 'qty': [pd.NA]})], axis=0)
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    csv_file_path = os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv')
    orders.to_csv(csv_file_path, header=False, index=False)
    



def calc_vwap_target_info(work_date, suffix=''):
    targets = get_target_parentorders(work_date, suffix)
    targets_df = targets.copy()
    targets_df['sym']=targets_df['symbol'].astype(int)
    targets_df['qty'] = targets_df['target'] - targets_df['position']
    targets_df['side']=targets_df['qty'].apply(lambda x:'b' if x>0 else 's')
    targets_df['qty'] = targets_df['qty'].abs()
    
    pre_date = Calendar.last_trading_day(work_date)
    preclose = get_stock_adj_close(pre_date)
    targets_df=targets_df.merge(preclose, left_on='sym', right_on='ticker', how='left')
    
    targets_df['hold_value'] = targets_df['position'] * targets_df['close']
    targets_df['target_value'] = targets_df['target'] * targets_df['close']
    targets_df['order_value'] = targets_df['qty'] * targets_df['close']
    
    hold_value = targets_df['hold_value'].sum()
    target_value = targets_df['target_value'].sum()
    buy_volume = targets_df['qty'].loc[targets_df['side']=='b'].sum()
    sell_volume = targets_df['qty'].loc[targets_df['side']=='s'].sum()

    buy_value = targets_df['order_value'].loc[targets_df['side']=='b'].sum()
    sell_value = targets_df['order_value'].loc[targets_df['side']=='s'].sum()
    buy_ratio = buy_value / hold_value if hold_value != 0 else 0
    sell_ratio = sell_value / hold_value if hold_value != 0 else 0
    num_orders = targets_df[targets_df['qty'] != 0].shape[0]
    print('----------------')
    print(f'账户买入: {buy_volume:,.0f} 股  {buy_value:,.0f} ')
    print(f'账户卖出: {sell_volume:,.0f} 股  {sell_value:,.0f} ')
    print(f'持仓市值: {hold_value:,.0f}')
    print(f'目标市值: {target_value:,.0f}') 
    print(f'净买: {(buy_value - sell_value):,.0f}')
    print(f'买入比例: {buy_ratio:.2%}')
    print(f'卖出比例: {sell_ratio:.2%}')
    print(f'总订单数: {num_orders}')
    print('----------------')
    
    


def t0_target(work_date, suffix=''):
    targets = get_target_parentorders(work_date, suffix='')
    targets_df = targets.copy()
    targets_df['sym']=targets_df['symbol'].astype(int).apply(lambda x:str(x).zfill(6))
    targets_df['sym'] = targets_df['sym'].apply(GetExchange)
    targets_df['lot_size']=targets_df['sym'].apply(stk_lot_size)
    targets_df = targets_df[targets_df['target']>=targets_df['lot_size']]
    targets_df['qty']=np.where(targets_df['position']>=targets_df['target'],targets_df['target'],targets_df['position'])
    targets_df = targets_df[targets_df['qty']>=targets_df['lot_size']]
    targets_df['side']='s'
    targets_df['type']=1
    targets_df['qty']=(targets_df['qty']/targets_df['lot_size']).astype(int)*targets_df['lot_size']
    targets_df = targets_df[targets_df['qty'] != 0]
    targets_df=targets_df[['type', 'sym', 'side', 'qty']]
    print(targets_df.head(3))

    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    csv_file_path = os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv')

    targets_df.to_csv(csv_file_path, header=False, index=False)
    append_symbol = '0'
    with open(csv_file_path, 'a') as file:
        file.write(append_symbol)
        file.close()

def ftpcopy(work_date, suffix=''):
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')

    # vwap
    sftp_clent_wintrader.put(local_path=os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'),
                             remote_path='交易文件\\超量子中泰\\zhongtai_vwap_target_{}{}.csv'.format(work_date,suffix)
                             )    
    ftp_clent_chaolz.put(os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'),
                         'vwap_target/zhongtai_vwap_target_{}{}.csv'.format(work_date,suffix))


    # t0
    sftp_clent_wintrader.put(local_path=os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'),
                             remote_path='交易文件\\超量子中泰\\zhongtai_t0_target_{}{}.csv'.format(work_date,suffix)
                             )    
    ftp_clent_chaolz.put(os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'),
                         't0_target/zhongtai_t0_target_{}{}.csv'.format(work_date,suffix))




    # shutil.copy('zhongtai_t0_target_{}.csv'.format(work_date), "/data/shared-data/public/vsftp_data/chaolz/t0_target/")
    # shutil.copy('zhongtai_vwap_target_{}.csv'.format(work_date), "/data/shared-data/public/vsftp_data/chaolz/vwap_target/")

if __name__ == "__main__":
    work_date=None
    # work_date='********'
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
    
    suffix='_test'
    print(f'\nsuffix: {suffix}')
    
    print(f'\n交易日: {work_date}\n')
    
    calc_vwap_target_info(work_date, suffix)
    vwap_target(work_date, suffix)
    # t0_target(work_date, suffix)
    # ftpcopy(work_date, suffix)
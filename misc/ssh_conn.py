import os
import socket
import paramiko
import sshtunnel
import time
import ftplib
import queue
from loguru import logger
# from typing import Literal
import threading


from typing import List
try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

from misc.zzw_tun import tun_kf_trade_dict
from accounts_config.sys_config import SYS_USER

private_key_path = f'/home/<USER>/.ssh/id_rsa'
private_key = paramiko.RSAKey.from_private_key_file(private_key_path)


ssh_conf_dict = {
    'dav' : {
        'conf' : {
            'hostname': '127.0.0.1',
            'username': 'zhanw',
            'port': 22,
            'private_key_path': private_key_path,
        },
        'prefix' : '/home/<USER>/dav',
        # 'conf' : {
        #     # 'hostname': '*************',
        #     'hostname': '*************',
        #     'username': 'trade',
        #     'port': 2022,
        #     'private_key_path': private_key_path
        # },
        # 'prefix' : '/'
    },
    'outer' : {
        'conf' : {
            'hostname': '**************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/data/ftp'
    },
    'inner' : {
        'conf' : {
            'hostname': '************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/home/<USER>/signal/product',
        # 'prefix' : '/home/<USER>/signal/account_system/product'
    },
    'ninner' : {
        'conf' : {
            'hostname': '************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/home/<USER>/signal/account_system/product'
    },
    'trade' : {
        'conf' : {
            'hostname': '************',
            'username': 'admin',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:'
    },
    'trade2' : {
        'conf' : {
            'hostname': '*************',
            'username': 'admin',
            'port': 17021,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:'
    },
    'trade3' : {
        'conf' : {
            'hostname': '127.0.0.1',
            'username': 'admin',
            'port': 16061,
            'private_key_path': private_key_path
        },
        'prefix' : 'D:'
    },
    'gtapi' : {
        # old aws
        # 'conf' : {
        #     'hostname': '*************',
        #     'username': 'api',
        #     'port': 2022,
        #     'private_key_path': private_key_path
        # },
        # 'prefix' : '/product'

        # new jeo ftp
        # 'conf' : {
        #     'hostname': '**************',
        #     'username': 'kf_trader',
        #     'port': 2022,
        #     'private_key_path': private_key_path,
        #     'password': 'kf_trader@KF2024'
        # },
        # 'prefix' : '/product'

        # local ftp
        'conf' : {
            'hostname': '*************',
            'username': 'api',
            'port': 2022,
            'private_key_path': private_key_path,
            'password': 'api123'
        },
        'prefix' : '/product'
    },
    'aws' : {
        'conf' : {
            'hostname': '**********',
            'username': 'zhanw',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:/Users/<USER>/trade'
    },
    'work' : {
        'conf' : {
            'hostname': '*************',
            'username': 'admin',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'D:/交易生产'
    },

    # zhishu
    'wintrader' : {
        'conf' : {
            'hostname': '*************',
            'username': 'Administrator',
            'port': 22,
            'private_key_path': private_key_path,
            'is_win_path' : True
        },
        'prefix' : 'D:',
    },
    'zsdav' : {
        'conf' : {
            'hostname': '127.0.0.1',
            'username': 'zhanw',
            'port': 22,
            'private_key_path': private_key_path,
        },
        'prefix' : '/home/<USER>/dav',
    },
}


ftp_conf_dict = {
    'jeff': {
        'conf' : {
            # 'hostname': '*************',
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'pengjf',
            'password': 'pengjf@KF2024',
            'port': 21,
        },
        'prefix' : '/product'
    },
    'ninner': {
        'conf' : {
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'signal',
            'password': 'plmOKNji983',
            'port': 21,
        },
        'prefix' : '/account_system/product'
    },
    'chenyc': {
        'conf' : {
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'chenyc',
            'password': 'chenyc@KF20250106342',
            'port': 21,
        },
        'prefix' : '/future_signal/strategy'
    },
    'it': {
        'conf' : {
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'it',
            'password': 'it@KF20250111',
            'port': 21,
        },
        'prefix' : '/future_strategy'
    },
    'jerry': {
        'conf' : {
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'signal',
            'password': 'plmOKNji983',
            'port': 21,
        },
        'prefix' : '/account_system/future_strategy'
    },
    'hexy': {
        'conf' : {
            'hostname': '************',    # 内网
            # 'hostname': '************',    # 外网
            'username': 'hexy',
            'password': 'hexy@KF202524538',
            'port': 21,
        },
        'prefix' : '/future_signal/strategy'
    },

    # zhishu
    'zx_zhongtai' : {
        'conf' : {
            'hostname': '************', 
            'username': 'zs_zhongtai',
            'password': 'zs_zhongtai_1qw2',
            'port': 21,
        },
        'prefix' : '/data'
    },
    'chaolz' : {
        'conf' : {
            'hostname': '************', 
            'username': 'chaolz',
            'password': 'clz_qKx55',
            'port': 21,
        },
        'prefix' : '/'
    }
}


DEFAULT_POOL_SIZE = 4


class SFTP_CLIENT:
    _pools = {}  # 维护所有 remote 类型的连接池
    _lock = threading.Lock()  # 全局锁，用于线程安全管理连接池

    def __new__(cls, remote: Literal[
            'dav', 
            'outer', 
            'inner', 
            'trade', 
            'trade2', 
            'trade3', 
            'gtapi', 
            'aws', 
            'work', 
            'ninner'], pool_size=None):
        """
        重写new方法，确保每个remote使用对应的连接池
        """
        if remote not in cls._pools:
            with cls._lock:
                if remote not in cls._pools:  # 双重检查锁，避免竞态条件
                    cls._pools[remote] = queue.Queue(maxsize=pool_size or DEFAULT_POOL_SIZE)  # 每种remote类型一个连接池

        instance = super(SFTP_CLIENT, cls).__new__(cls)
        return instance
    
    def __init__(self, remote: Literal[
            'dav', 
            'outer', 
            'inner', 
            'trade',
            'trade2', 
            'trade3', 
            'gtapi',
            'aws',
            'work',
            'ninner',
            'wintrader',
            ]):
        if not hasattr(self, 'initialized'):  # 确保初始化仅执行一次

            ssh_conf = ssh_conf_dict[remote]['conf']
            prefix = ssh_conf_dict[remote]['prefix']
            # password = ssh_conf.get('password')

            self._lock = threading.Lock()
            self.remote_prefix = prefix
            self.remote = remote
            self.host = ssh_conf['hostname']
            self.username = ssh_conf['username']
            self.password = ssh_conf.get('password')
            self.private_key_path = ssh_conf.get('private_key_path')
            self.port = ssh_conf['port']
            self.transport = None
            self.sftp = None
            self.initialized = True
            self.is_win_path = ssh_conf.get('is_win_path', False)

    def _get_from_pool(self):
        """从连接池中获取一个连接，如果连接池为空则创建新的连接"""
        pool = self._pools[self.remote]
        try:
            client = pool.get(block=False)  # 尝试从连接池获取
            if not client._is_connected():
                client._reconnect()
        except queue.Empty:
            client = self  # 如果池中没有可用连接，则使用当前对象
            client._connect()
        return client

    def _return_to_pool(self):
        """将当前连接放回连接池"""
        pool = self._pools[self.remote]
        if pool.full():
            self.close()  # 如果池已满，关闭当前连接
        else:
            pool.put(self)  # 归还到池中


    def _connect(self):
        private_key = paramiko.RSAKey(filename=self.private_key_path)
        self.transport = paramiko.Transport((self.host, self.port))
        self.transport.set_keepalive(60)
        self.transport.connect(username=self.username, pkey=private_key, password=self.password)
        self.sftp = paramiko.SFTPClient.from_transport(self.transport)

    def _reconnect(self):
        self.close()
        self._connect()

    def _is_connected(self):
        return self.transport is not None and self.transport.is_active() and self.sftp is not None

    def close(self):
        if self._is_connected():
            self.sftp.close()
            self.transport.close()
            self.sftp = None
            self.transport = None

    def put(self, local_path, remote_path, confirm=True):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try copying to {} \nsource: {} \ndest : {}'.format(self.remote, local_path, remote_path))
        with self._lock:
            client = self._get_from_pool()  # 从池中获取连接
            try:
                client.sftp.put(local_path, remote_path, confirm=confirm)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                client.sftp.put(local_path, remote_path, confirm=confirm)
            finally:
                client._return_to_pool()  # 归还连接到池中


    def get(self, remote_path, local_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try copying from {} \nsource: {} \ndest : {}'.format(self.remote, remote_path, local_path))

        with self._lock:
            client = self._get_from_pool()  # 从池中获取连接
            try:
                client.sftp.get(remote_path, local_path)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                print(f'remote_path:{remote_path}  local_path:{local_path}')
                client.sftp.get(remote_path, local_path)
            finally:
                client._return_to_pool()  # 归还连接到池中

    def mkdir(self, remote_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try making directory {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            client = self._get_from_pool()
            try:
                client.sftp.mkdir(remote_path)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                client.sftp.mkdir(remote_path)
            finally:
                client._return_to_pool()
            logger.info('mkdir finished: {}'.format(remote_path))

    def chmod(self, remote_path, mode=511):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try making directory {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            client = self._get_from_pool()
            try:
                client.sftp.chmod(remote_path, mode)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                client.sftp.chmod(remote_path, mode)
            finally:
                client._return_to_pool()
            logger.info('chmod finished: {}'.format(remote_path))

    def listdir(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try listing {} \ndirectory: {}'.format(self.remote, remote_path))
        # check if lock is ready:
        with self._lock:
            client = self._get_from_pool()
            try:
                result = client.sftp.listdir(remote_path)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                result = client.sftp.listdir(remote_path)
            finally:
                client._return_to_pool()
            return result

    def remove(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        if self.is_win_path:
            remote_path = remote_path.replace("/", "\\")
        # logger.info('try listing {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            client = self._get_from_pool()
            try:
                client.sftp.remove(remote_path)
            except (FileNotFoundError, paramiko.SSHException, socket.error):
                client._reconnect()
                client.sftp.remove(remote_path)
            finally:
                client._return_to_pool()
            logger.info('remove finished: {}'.format(remote_path))
    
    def exe_command(self, command):
        # remote_path = os.path.join(self.remote_prefix, remote_path)
        with self._lock:
            logger.info('try executing command: {} '.format(command))
            client = self._get_from_pool()
            try:
                ssh = paramiko.SSHClient()
                ssh._transport = client.transport
                stdin, stdout, stderr = ssh.exec_command(command)
                print("Output:", stdout.read())
                print("Errors:", stderr.read())
            finally:
                client._return_to_pool()

    def cp_at_remotedir(self, src, dest):
        remote_src = os.path.join(self.remote_prefix, src)
        remote_dest = os.path.join(self.remote_prefix, dest)
        logger.info('try copy at remote {}: \nsource: {} \ndest : {}'.format(self.remote, remote_src, remote_dest))
        command = f'cp {remote_src} {remote_dest}'
        print(command)

        with self._lock:
            client = self._get_from_pool()
            try:
                client.exe_command(command)
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found error: \nsource:{} \ndest: {}'.format(remote_src, remote_dest))
            # logger.info('copied to remote path finished: {}'.format(remote_path))
            finally:
                client._return_to_pool()


class FTP_Client:

    def __init__(
        self,
        remote: str,
        # host: str,
        # username: str,
        # password: str,
        # port: int = 21,
        # remote_prefix: str = "/",
        # log_file: str = "logs/ftp_client.log",
        keep_alive_interval: int = 200,
    ):
        """
        初始化 FTP 客户端
        :param remote: 连接标识符
        :param host: FTP 服务器地址
        :param username: 用户名
        :param password: 密码
        :param port: 端口号
        :param remote_prefix: 远程根路径，默认值为 "/"
        :param log_file: 日志文件路径
        :param keep_alive_interval: Keep-Alive 心跳间隔（秒），默认 300 秒
        """
        self.remote = remote
        self.host = ftp_conf_dict[remote]["conf"]["hostname"]
        self.username = ftp_conf_dict[remote]["conf"]["username"]
        self.password = ftp_conf_dict[remote]["conf"]["password"]
        self.port = ftp_conf_dict[remote]["conf"]["port"]
        self.remote_prefix = ftp_conf_dict[remote]["prefix"].rstrip("/")
        # self.local_storage = threading.local()  # 每个线程维护独立的 FTP 连接
        self._lock = threading.Lock()
        self.ftp = None

        self.keep_alive_interval = keep_alive_interval
        self.keep_alive_thread = None
        self.keep_alive_running = False
        # 配置日志记录
        # if not os.path.exists(os.path.dirname(log_file)):
        #     os.makedirs(os.path.dirname(log_file))
        # logger.add(log_file, rotation="1 MB", retention="7 days", compression="zip")
        # logger.info(f"FTPClient initialized for remote: {remote}, host: {host}, remote_prefix: {self.remote_prefix}")

    def _connect(self):
        """确保当前线程有一个有效的 FTP 连接"""
        # if not hasattr(self.local_storage, 'ftp') or not self.local_storage.ftp.sock:
        try:
            ftp = ftplib.FTP()
            ftp.connect(self.host, self.port)  # 设置较大的超时时间
            ftp.login(self.username, self.password)
            ftp.set_pasv(True)
            ftp.cwd(self.remote_prefix)
            ftp.encoding = 'utf-8'
            # self.local_storage.ftp = ftp
            self.ftp = ftp
            # logger.info(f"Thread {threading.current_thread().name} connected to FTP server {self.host}")
            if not self.keep_alive_running:
                self._start_keep_alive()
        except ftplib.all_errors as e:
            logger.error(f"failed to connect to {self.host}: {e}")
            raise

    def _get_ftp(self):
        """获取当前线程的 FTP 连接，并检查是否有效"""
        try:
            # self._connect()
            # 测试连接是否活跃
            # self.local_storage.ftp.voidcmd("NOOP")
            with self._lock:
                self.ftp.voidcmd("NOOP")
        except:
            logger.warning(f"connecting ftp {self.remote}.")
            self._connect()  # 重新连接"""
        # return self.local_storage.ftp
        return self.ftp
    
    def _start_keep_alive(self):
        """启动 Keep-Alive 心跳线程"""
        if not self.keep_alive_running:
            self.keep_alive_running = True
            self.keep_alive_thread = threading.Thread(target=self._keep_alive_loop, daemon=True)
            self.keep_alive_thread.start()

    def _stop_keep_alive(self):
        """停止 Keep-Alive 心跳线程"""
        self.keep_alive_running = False
        if self.keep_alive_thread:
            self.keep_alive_thread.join()
            self.keep_alive_thread = None

    def _keep_alive_loop(self):
        """Keep-Alive 循环"""
        while self.keep_alive_running:
            try:
                # if hasattr(self.local_storage, 'ftp') and self.local_storage.ftp.sock:
                with self._lock:
                    self.ftp.voidcmd("NOOP")  # 发送 NOOP 命令保持连接
                # self.local_storage.ftp.voidcmd("NOOP")  # 发送 NOOP 命令保持连接
                # logger.info(f"sent keep-alive signal to {self.host}")
            except ftplib.all_errors as e:
                logger.warning(f"Keep-alive failed: {e}")
                self._connect()  # 重新连接
            time.sleep(self.keep_alive_interval)
            
            

    def listdir(self, remote_path: str = "") -> List[str]:
        """
        列出远程目录中的文件
        :param remote_path: 相对于 remote_prefix 的路径
        :return: 文件名列表
        """
        ftp = self._get_ftp()
        target_path = os.path.join(self.remote_prefix, remote_path).rstrip("/")
        try:
            with self._lock:
                files = ftp.nlst(target_path)
            # logger.info(f"Thread {threading.current_thread().name} listed files in directory '{target_path}': {files}")
            return  [os.path.basename(file) for file in files]
        except ftplib.error_perm as e:
            logger.error(f"failed to list directory '{target_path}': {e}")
            raise

    def put(self, local_path: str, remote_path: str):
        """
        上传文件到远程服务器
        :param local_path: 本地文件路径
        :param remote_path: 相对于 remote_prefix 的远程路径
        :param confirm: 上传确认（仅占位，FTP 不直接支持）
        """
        ftp = self._get_ftp()
        target_path = os.path.join(self.remote_prefix, remote_path).rstrip("/")
        try:
            with self._lock:
                with open(local_path, 'rb') as f:
                    ftp.storbinary(f"STOR {target_path}", f)
            # with open(local_path, 'rb') as f:
            #     ftp.storbinary(f"STOR {target_path}", f)
            # logger.info(f"Thread {threading.current_thread().name} uploaded file '{local_path}' to '{target_path}'")
        except FileNotFoundError:
            logger.error(f"local file '{local_path}' does not exist")
        except ftplib.all_errors as e:
            logger.error(f"failed to upload file '{local_path}' to '{target_path}': {e}")
            raise

    def get(self, remote_path: str, local_path: str):
        """
        下载远程文件到本地
        :param remote_path: 相对于 remote_prefix 的远程路径
        :param local_path: 本地目标路径
        """
        ftp = self._get_ftp()
        target_path = os.path.join(self.remote_prefix, remote_path).rstrip("/")
        try:
            with self._lock:
                with open(local_path, 'wb') as f:
                    ftp.retrbinary(f"RETR {target_path}", f.write)
            # with open(local_path, 'wb') as f:
            #     ftp.retrbinary(f"RETR {target_path}", f.write)
            # logger.info(f"Thread {threading.current_thread().name} downloaded file '{target_path}' to '{local_path}'")
        except ftplib.error_perm as e:
            logger.error(f"failed to download file '{target_path}' to '{local_path}': {e}")
            # remove local empty file
            os.remove(local_path)
            raise

    def mkdir(self, remote_path: str):
        """
        创建远程目录
        :param remote_path: 相对于 remote_prefix 的目录路径
        """
        ftp = self._get_ftp()
        target_path = os.path.join(self.remote_prefix, remote_path).rstrip("/")
        try:
            with self._lock:
                ftp.mkd(target_path)
            # ftp.mkd(target_path)
            logger.info(f"created directory '{target_path}'")
        except ftplib.error_perm as e:
            logger.error(f"failed to create directory '{target_path}': {e}")
            raise

    def remove(self, remote_path: str):
        """
        删除远程文件
        :param remote_path: 相对于 remote_prefix 的文件路径
        """
        ftp = self._get_ftp()
        target_path = os.path.join(self.remote_prefix, remote_path).rstrip("/")
        try:
            with self._lock:
                ftp.delete(target_path)
            # ftp.delete(target_path)
            logger.info(f"deleted file '{target_path}'")
        except ftplib.error_perm as e:
            logger.error(f"failed to delete file '{target_path}': {e}")
            raise

    def close(self):
        """关闭当前线程的 FTP 连接"""
        # if hasattr(self.local_storage, 'ftp') and self.local_storage.ftp:
        if hasattr(self, 'ftp') and self.ftp:
            try:
                with self._lock:
                    self.ftp.quit()
                logger.info(f"FTP connection closed")
                # self.local_storage.ftp.quit()
                # logger.info(f"Thread {threading.current_thread().name} FTP connection closed")
            except ftplib.all_errors as e:
                logger.error(f"error while closing connection: {e}")
            finally:
                self.ftp = None


# sftp_clent_inner = SFTP_CLIENT('inner')
# sftp_clent_ninner = SFTP_CLIENT('ninner')
# sftp_clent_outer = SFTP_CLIENT('outer')
sftp_clent_dav = SFTP_CLIENT('dav')
# sftp_clent_trade = SFTP_CLIENT('trade')
# sftp_clent_trade2 = SFTP_CLIENT('trade2')
# sftp_clent_trade3 = SFTP_CLIENT('trade3')
# sftp_clent_gtapi = SFTP_CLIENT('gtapi')
# sftp_clent_aws = SFTP_CLIENT('aws')
# sftp_clent_work = SFTP_CLIENT('work')
sftp_clent_wintrader = SFTP_CLIENT('wintrader')
sftp_clent_zsdav = SFTP_CLIENT('zsdav')


# ftp_clent_jeff = FTP_Client(
#     remote="jeff",
#     host=ftp_conf_dict["jeff"]["conf"]["hostname"],
#     username=ftp_conf_dict["jeff"]["conf"]["username"],
#     password=ftp_conf_dict["jeff"]["conf"]["password"],
#     port=ftp_conf_dict["jeff"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["jeff"]["prefix"],
# )

# sftp_clent_ninner = FTP_Client(
#     remote="ninner",
#     host=ftp_conf_dict["ninner"]["conf"]["hostname"],
#     username=ftp_conf_dict["ninner"]["conf"]["username"],
#     password=ftp_conf_dict["ninner"]["conf"]["password"],
#     port=ftp_conf_dict["ninner"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["ninner"]["prefix"],
# )

# ftp_clent_chenyc = FTP_Client(
#     remote="chenyc",
#     host=ftp_conf_dict["chenyc"]["conf"]["hostname"],
#     username=ftp_conf_dict["chenyc"]["conf"]["username"],
#     password=ftp_conf_dict["chenyc"]["conf"]["password"],
#     port=ftp_conf_dict["chenyc"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["chenyc"]["prefix"],
# )

# ftp_clent_it = FTP_Client(
#     remote="it",
#     host=ftp_conf_dict["it"]["conf"]["hostname"],
#     username=ftp_conf_dict["it"]["conf"]["username"],
#     password=ftp_conf_dict["it"]["conf"]["password"],
#     port=ftp_conf_dict["it"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["it"]["prefix"],
# )

# ftp_clent_jerry = FTP_Client(
#     remote="jerry",
#     host=ftp_conf_dict["jerry"]["conf"]["hostname"],
#     username=ftp_conf_dict["jerry"]["conf"]["username"],
#     password=ftp_conf_dict["jerry"]["conf"]["password"],
#     port=ftp_conf_dict["jerry"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["jerry"]["prefix"],
# )

# ftp_clent_hexy = FTP_Client(
#     remote="hexy",
#     host=ftp_conf_dict["hexy"]["conf"]["hostname"],
#     username=ftp_conf_dict["hexy"]["conf"]["username"],
#     password=ftp_conf_dict["hexy"]["conf"]["password"],
#     port=ftp_conf_dict["hexy"]["conf"]["port"],
#     remote_prefix=ftp_conf_dict["hexy"]["prefix"],
# )

ftp_clent_zx_zhongtai = FTP_Client(
    remote="zx_zhongtai",
    # host=ftp_conf_dict["zx_zhongtai"]["conf"]["hostname"],
    # username=ftp_conf_dict["zx_zhongtai"]["conf"]["username"],
    # password=ftp_conf_dict["zx_zhongtai"]["conf"]["password"],
    # port=ftp_conf_dict["zx_zhongtai"]["conf"]["port"],
    # remote_prefix=ftp_conf_dict["zx_zhongtai"]["prefix"],
)
ftp_clent_chaolz = FTP_Client(
    remote="chaolz",
)
sftp_methods = {
    # 'inner': sftp_clent_inner,
    # 'outer': sftp_clent_outer,
    # 'dav'  : sftp_clent_dav,
    # 'trade': sftp_clent_trade,
    # 'trade2': sftp_clent_trade2,
    # 'trade3': sftp_clent_trade3,
    # 'gtapi': sftp_clent_gtapi,
    # 'aws'  : sftp_clent_aws,
    # 'work' : sftp_clent_work,
    # 'ninner': sftp_clent_ninner,
    # 'jeff': ftp_clent_jeff,
    # 'chenyc': ftp_clent_chenyc,
    # 'it': ftp_clent_it,
    # 'jerry': ftp_clent_jerry,
    # 'hexy': ftp_clent_hexy,
    'zx_zhongtai' : ftp_clent_zx_zhongtai,
    'chaolz'     : ftp_clent_chaolz,
    'wintrader'  : sftp_clent_wintrader,
    'zsdav'      : sftp_clent_zsdav,
    'dav'        : sftp_clent_dav,
}    

sftps = [
    'wintrader',
    'zsdav',
]
ftps = [
    'zx_zhongtai',
    'chaolz'
]